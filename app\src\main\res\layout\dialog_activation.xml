<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="@dimen/vs_520"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="vertical"
        android:padding="@dimen/vs_30">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/vs_20"
            android:text="应用激活"
            android:textColor="@color/color_CC000000"
            android:textSize="@dimen/ts_28"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/vs_20"
            android:lineSpacingMultiplier="1.5"
            android:text="请输入激活码以继续使用应用"
            android:textAlignment="center"
            android:textColor="@color/color_CC000000"
            android:textSize="@dimen/ts_24" />

        <EditText
            android:id="@+id/etActivationCode"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_20"
            android:background="@drawable/input_dialog_api_input"
            android:hint="请输入激活码"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:maxLines="1"
            android:paddingLeft="@dimen/vs_10"
            android:paddingTop="@dimen/vs_12"
            android:paddingRight="@dimen/vs_10"
            android:paddingBottom="@dimen/vs_12"
            android:textColor="@color/color_CC000000"
            android:textColorHint="@color/color_6C3D3D3D"
            android:textSize="@dimen/ts_26"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:nextFocusDown="@id/btnActivate" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/btnActivate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/vs_10"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:gravity="center"
                android:padding="@dimen/vs_12"
                android:text="激活"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_24"
                android:nextFocusUp="@id/etActivationCode"
                android:nextFocusRight="@id/btnCancel" />

            <TextView
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/vs_10"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:focusableInTouchMode="false"
                android:gravity="center"
                android:padding="@dimen/vs_12"
                android:text="退出"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_24"
                android:nextFocusUp="@id/etActivationCode"
                android:nextFocusLeft="@id/btnActivate" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout> 