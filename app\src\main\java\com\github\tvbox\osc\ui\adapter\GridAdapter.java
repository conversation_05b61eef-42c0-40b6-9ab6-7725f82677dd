package com.github.tvbox.osc.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.picasso.RoundTransformation;
import com.github.tvbox.osc.util.DefaultConfig;
import com.github.tvbox.osc.util.ImgUtil;
import com.github.tvbox.osc.util.LocalCoverCacheUtil;
import com.github.tvbox.osc.util.MD5;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.util.ArrayList;

import me.jessyan.autosize.utils.AutoSizeUtils;

/**
 * GridAdapter 支持传入 style 来设置图片的宽高比例，
 * 如果不传 style 则保留旧的默认风格（XML 中 item_grid.xml 定义的尺寸）。
 */
public class GridAdapter extends BaseQuickAdapter<Movie.Video, BaseViewHolder> {
    private boolean mShowList ;
    private int defaultWidth;
    public ImgUtil.Style style; // 动态风格，传入时调整图片宽高比



    /**
     * 如果 style 传 null，则采用 item_grid.xml 中的默认尺寸
     */
    public GridAdapter(boolean showList, ImgUtil.Style style) {
        super( showList ? R.layout.item_list:R.layout.item_grid, new ArrayList<>());
        this.mShowList = showList;
        if(style!=null ){
            if(style.type.equals("list"))this.mShowList=true;
            this.defaultWidth=ImgUtil.getStyleDefaultWidth(style);
        }
        this.style = style;
    }

    @Override
    protected void convert(BaseViewHolder helper, Movie.Video item) {
        if(this.mShowList) {
            helper.setText(R.id.tvNote, item.note);
            helper.setText(R.id.tvName, item.name);
            ImageView ivThumb = helper.getView(R.id.ivThumb);
            //由于部分电视机使用glide报错
            if (!TextUtils.isEmpty(item.pic)) {
                item.pic=item.pic.trim();
                if(ImgUtil.isBase64Image(item.pic)){
                    // 如果是 Base64 图片，解码并设置
                    ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(item.pic));
                } else if (item.pic.startsWith("http") && "local".equals(item.sourceKey)) {
                    // 本地影片的网络封面，使用缓存工具类
                    String cachedPic = LocalCoverCacheUtil.getCachedCover(item.pic);
                    if (!cachedPic.equals(item.pic) && new File(cachedPic).exists()) {
                        // 已缓存到本地文件，使用文件路径加载
                        Picasso.get()
                                .load(new File(cachedPic))
                                .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                        .centerCorp(true)
                                        .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                        .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                .placeholder(R.drawable.img_loading_placeholder)
                                .noFade()
                                .error(ImgUtil.createTextDrawable(item.name))
                                .into(ivThumb);
                    } else {
                        // 还未缓存，先显示占位图，然后异步缓存并更新
                        // ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                        final String finalUrl = item.pic;
                        final String finalName = item.name;
                        final ImageView finalIvThumb = ivThumb;
                        LocalCoverCacheUtil.cacheNetworkCover(item.pic, new LocalCoverCacheUtil.CacheCallback() {
                            @Override
                            public void onResult(String result) {
                                if (!result.equals(finalUrl) && new File(result).exists()) {
                                    // 缓存完成，更新显示
                                    if (finalIvThumb.getContext() instanceof android.app.Activity) {
                                        ((android.app.Activity) finalIvThumb.getContext()).runOnUiThread(() -> {
                                            Picasso.get()
                                                    .load(new File(result))
                                                    .transform(new RoundTransformation(MD5.string2MD5(finalUrl))
                                                            .centerCorp(true)
                                                            .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                                            .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                                    .noFade()
                                                    .error(ImgUtil.createTextDrawable(finalName))
                                                    .into(finalIvThumb);
                                        });
                                    }
                                }
                            }
                        });
                    }
                }else {
                    Picasso.get()
                            .load(DefaultConfig.checkReplaceProxy(item.pic))
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext, 240), AutoSizeUtils.mm2px(mContext, 336))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(ImgUtil.createTextDrawable(item.name))
                            .into(ivThumb);
                }
            } else {
//                ivThumb.setImageResource(R.drawable.img_loading_placeholder);
                ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
            }
            return;
        }

        TextView tvYear = helper.getView(R.id.tvYear);
        if (item.year <= 0) {
            tvYear.setVisibility(View.GONE);
        } else {
            tvYear.setText(String.valueOf(item.year));
            tvYear.setVisibility(View.VISIBLE);
        }
        TextView tvLang = helper.getView(R.id.tvLang);
        tvLang.setVisibility(View.GONE);
        /*if (TextUtils.isEmpty(item.lang)) {
            tvLang.setVisibility(View.GONE);
        } else {
            tvLang.setText(item.lang);
            tvLang.setVisibility(View.VISIBLE);
        }*/
        TextView tvArea = helper.getView(R.id.tvArea);
        tvArea.setVisibility(View.GONE);
        /*if (TextUtils.isEmpty(item.area)) {
            tvArea.setVisibility(View.GONE);
        } else {
            tvArea.setText(item.area);
            tvArea.setVisibility(View.VISIBLE);
        }*/
        if (TextUtils.isEmpty(item.note)) {
            helper.setVisible(R.id.tvNote, false);
        } else {
            helper.setVisible(R.id.tvNote, true);
            helper.setText(R.id.tvNote, item.note);
        }
        helper.setText(R.id.tvName, item.name);
        helper.setText(R.id.tvActor, item.actor);
        ImageView ivThumb = helper.getView(R.id.ivThumb);

        int newWidth = ImgUtil.defaultWidth;
        int newHeight = ImgUtil.defaultHeight;
        if(style!=null){
            newWidth = defaultWidth;
            newHeight = (int)(newWidth / style.ratio);
        }

        //由于部分电视机使用glide报错
        if (!TextUtils.isEmpty(item.pic)) {
            item.pic=item.pic.trim();
            if(ImgUtil.isBase64Image(item.pic)){
                // 如果是 Base64 图片，解码并设置
                ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(item.pic));
            } else if (item.pic.startsWith("http") && "local".equals(item.sourceKey)) {
                // 本地影片的网络封面，使用缓存工具类
                String cachedPic = LocalCoverCacheUtil.getCachedCover(item.pic);
                if (!cachedPic.equals(item.pic) && new File(cachedPic).exists()) {
                    // 已缓存到本地文件，使用文件路径加载
                    Picasso.get()
                            .load(new File(cachedPic))
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext,newWidth), AutoSizeUtils.mm2px(mContext,newHeight))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(ImgUtil.createTextDrawable(item.name))
                            .into(ivThumb);
                } else {
                    // 还未缓存，先显示占位图，然后异步缓存并更新
                    ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                    final String finalUrl = item.pic;
                    final String finalName = item.name;
                    final ImageView finalIvThumb = ivThumb;
                    final int finalNewWidth = newWidth;
                    final int finalNewHeight = newHeight;
                    LocalCoverCacheUtil.cacheNetworkCover(item.pic, new LocalCoverCacheUtil.CacheCallback() {
                        @Override
                        public void onResult(String result) {
                            if (!result.equals(finalUrl) && new File(result).exists()) {
                                // 缓存完成，更新显示
                                if (finalIvThumb.getContext() instanceof android.app.Activity) {
                                    ((android.app.Activity) finalIvThumb.getContext()).runOnUiThread(() -> {
                                        Picasso.get()
                                                .load(new File(result))
                                                .transform(new RoundTransformation(MD5.string2MD5(finalUrl))
                                                        .centerCorp(true)
                                                        .override(AutoSizeUtils.mm2px(mContext, finalNewWidth), AutoSizeUtils.mm2px(mContext, finalNewHeight))
                                                        .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                                .noFade()
                                                .error(ImgUtil.createTextDrawable(finalName))
                                                .into(finalIvThumb);
                                    });
                                }
                            }
                        }
                    });
                }
            }else {
                Picasso.get()
                        .load(DefaultConfig.checkReplaceProxy(item.pic))
                        .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                .centerCorp(true)
                                .override(AutoSizeUtils.mm2px(mContext,newWidth), AutoSizeUtils.mm2px(mContext,newHeight))
                                .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                        .placeholder(R.drawable.img_loading_placeholder)
                        .noFade()
                        .error(ImgUtil.createTextDrawable(item.name))
                        .into(ivThumb);
            }
        } else {
            ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
        }
        applyStyleToImage(ivThumb);//动态设置宽高
    }

    /**
     * 根据传入的 style 动态设置 ImageView 的高度：高度 = 宽度 / ratio
     */
    private void applyStyleToImage(final ImageView ivThumb) {
        if(style!=null){
            ViewGroup container = (ViewGroup) ivThumb.getParent();
            int width = defaultWidth;
            int height = (int) (width / style.ratio);
            ViewGroup.LayoutParams containerParams = container.getLayoutParams();
            containerParams.height = AutoSizeUtils.mm2px(mContext, height); // 高度
            containerParams.width = AutoSizeUtils.mm2px(mContext, width); // 宽度
            container.setLayoutParams(containerParams);
        }
    }
}
