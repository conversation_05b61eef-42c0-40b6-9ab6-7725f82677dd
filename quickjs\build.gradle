plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 33

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28
        externalNativeBuild {
            cmake {
                abiFilters 'armeabi-v7a'
            }
        }
    }

    buildTypes {
        all {
            ndk {
                abiFilters 'armeabi-v7a'
            }
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    externalNativeBuild {
        /*cmake {
            path file('src/main/cpp/CMakeLists.txt')
            version "3.10.2"
        }*/
    }
}

dependencies {
}