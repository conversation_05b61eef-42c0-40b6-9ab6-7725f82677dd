<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/mGridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:paddingLeft="@dimen/vs_60"
        android:paddingTop="@dimen/vs_10"
        android:paddingRight="@dimen/vs_60"
        android:paddingBottom="@dimen/vs_10"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
</LinearLayout>