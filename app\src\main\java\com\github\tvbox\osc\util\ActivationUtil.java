package com.github.tvbox.osc.util;

import android.text.TextUtils;

import com.github.tvbox.osc.util.urlhttp.OKCallBack;
import com.github.tvbox.osc.util.urlhttp.OkHttpUtil;
import com.orhanobut.hawk.Hawk;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;

import java.util.concurrent.TimeUnit;

public class ActivationUtil {
    private static final String ACTIVATION_URL = "http://106.75.225.184/verify.php";

    /**
     * 检查是否已激活
     */
    public static boolean isActivated() {
        return Hawk.get(HawkConfig.ACTIVATION_STATUS, false);
    }

    /**
     * 检查激活服务器是否可用
     */
    public static void checkActivationServerAvailable(ServerCheckCallback callback) {
        // 在后台线程中执行HEAD请求
        new Thread(() -> {
            try {
                // 创建专用的短超时客户端（3秒超时）
                OkHttpClient client = createShortTimeoutClient();
                okhttp3.Request request = new okhttp3.Request.Builder()
                        .url(ACTIVATION_URL)
                        .head() // 使用HEAD方法
                        .build();

                Call call = client.newCall(request);
                Response response = call.execute();

                // 检查响应状态码，200-299范围内认为服务器可用
                boolean isAvailable = response.isSuccessful();
                callback.onResult(isAvailable);

            } catch (Exception e) {
                // 网络异常或其他错误，认为服务器不可用
                callback.onResult(false);
            }
        }).start();
    }

    /**
     * 创建短超时时间的OkHttpClient（3秒超时）
     * 专用于激活服务器可用性检查
     */
    private static OkHttpClient createShortTimeoutClient() {
        return OkGoHelper.getDefaultClient().newBuilder()
                .connectTimeout(3, TimeUnit.SECONDS)    // 连接超时3秒
                .readTimeout(3, TimeUnit.SECONDS)       // 读取超时3秒
                .writeTimeout(3, TimeUnit.SECONDS)      // 写入超时3秒
                .build();
    }
    
    /**
     * 获取已保存的激活码
     */
    public static String getSavedActivationCode() {
        return Hawk.get(HawkConfig.ACTIVATION_CODE, "");
    }
    
    /**
     * 保存激活状态
     */
    public static void saveActivationStatus(boolean isActivated, String activationCode) {
        Hawk.put(HawkConfig.ACTIVATION_STATUS, isActivated);
        if (isActivated && !TextUtils.isEmpty(activationCode)) {
            Hawk.put(HawkConfig.ACTIVATION_CODE, activationCode);
        }
    }
    
    /**
     * 验证激活码
     */
    public static void verifyActivationCode(String activationCode, ActivationCallback callback) {
        if (TextUtils.isEmpty(activationCode)) {
            callback.onFailure("激活码不能为空");
            return;
        }
        
        // 在后台线程中执行网络请求
        new Thread(() -> {
            try {
                Map<String, String> params = new HashMap<>();
                params.put("code", activationCode);
                
                OkHttpUtil.post(OkGoHelper.getDefaultClient(), ACTIVATION_URL, params, new OKCallBack<String>() {
                    @Override
                    public String onParseResponse(Call call, Response response) {
                        try {
                            return response.body().string();
                        } catch (Exception e) {
                            return "";
                        }
                    }
                    
                    @Override
                    public void onFailure(Call call, Exception e) {
                        callback.onFailure("网络请求失败: " + e.getMessage());
                    }
                    
                    @Override
                    public void onResponse(String response) {
                        try {
                            if (TextUtils.isEmpty(response)) {
                                callback.onFailure("服务器响应为空");
                                return;
                            }
                            
                            JSONObject jsonObject = new JSONObject(response);
                            int code = jsonObject.optInt("code", -1);
                            String message = jsonObject.optString("message", "未知错误");
                            
                            if (code == 0) {
                                // 激活成功
                                saveActivationStatus(true, activationCode);
                                callback.onSuccess("激活成功");
                            } else {
                                // 激活失败
                                callback.onFailure("激活失败: " + message);
                            }
                        } catch (Exception e) {
                            callback.onFailure("解析响应失败: " + e.getMessage());
                        }
                    }
                });
            } catch (Exception e) {
                callback.onFailure("请求异常: " + e.getMessage());
            }
        }).start();
    }
    
    /**
     * 清除激活状态（用于测试或重置）
     */
    public static void clearActivationStatus() {
        Hawk.put(HawkConfig.ACTIVATION_STATUS, false);
        Hawk.put(HawkConfig.ACTIVATION_CODE, "");
    }
    
    public interface ActivationCallback {
        void onSuccess(String message);
        void onFailure(String error);
    }

    public interface ServerCheckCallback {
        void onResult(boolean isAvailable);
    }
}