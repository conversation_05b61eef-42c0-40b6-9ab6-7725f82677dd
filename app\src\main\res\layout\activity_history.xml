<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_50"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_50"
        android:paddingBottom="@dimen/vs_20">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_50"
            android:drawablePadding="@dimen/vs_10"
            android:gravity="center"
            android:text="历史记录"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_34"
            android:textStyle="bold" />

        <Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tvDelTip"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/vs_50"
            android:layout_gravity="center"
            android:layout_marginRight="@dimen/vs_10"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center"
            android:text="确定键删除当前选中记录"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_24"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/tvDelete"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_10"
            android:layout_marginEnd="@dimen/vs_10"
            android:gravity="center"
            android:background="@drawable/button_dialog_vod"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:padding="@dimen/vs_5"
            android:src="@drawable/icon_delete" />

        <ImageView
            android:id="@+id/tvClear"
            android:layout_width="@dimen/vs_50"
            android:layout_height="@dimen/vs_50"
            android:layout_marginStart="@dimen/vs_10"
            android:layout_marginEnd="@dimen/vs_10"
            android:gravity="center"
            android:background="@drawable/button_dialog_vod"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:padding="@dimen/vs_5"
            android:src="@drawable/icon_clear" />
    </LinearLayout>

    <com.owen.tvrecyclerview.widget.TvRecyclerView
        android:id="@+id/mGridView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:paddingLeft="@dimen/vs_60"
        android:paddingTop="@dimen/vs_10"
        android:paddingRight="@dimen/vs_60"
        android:paddingBottom="@dimen/vs_10"
        app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
        app:tv_selectedItemIsCentered="true"
        app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
</LinearLayout>