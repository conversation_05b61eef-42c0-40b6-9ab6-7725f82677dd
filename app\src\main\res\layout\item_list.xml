<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="@drawable/shape_user_focus"
    android:focusable="true">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        >

        <ImageView
            android:id="@+id/ivThumb"
            android:layout_width="@dimen/vs_50"
            android:layout_height="match_parent"
            android:padding="@dimen/vs_1"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/tvName"
            android:layout_weight="1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="marquee"
            android:gravity="center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:padding="@dimen/vs_10"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_20" />

        <TextView
            android:id="@+id/tvNote"
            android:layout_weight="4"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:ellipsize="marquee"
            android:gravity="end"
            android:marqueeRepeatLimit="marquee_forever"
            android:padding="@dimen/vs_10"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_20" />


    </LinearLayout>


</FrameLayout>