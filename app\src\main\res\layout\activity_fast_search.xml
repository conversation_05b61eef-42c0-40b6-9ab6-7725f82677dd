<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_10"
        android:layout_marginBottom="@dimen/vs_10"
        android:paddingLeft="20mm">

        <TextView
            android:id="@+id/mSearchTitle"
            android:layout_width="@dimen/vs_200"
            android:layout_height="match_parent"
            android:text="搜索(0/0)"
            android:layout_gravity="center_horizontal"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:paddingTop="@dimen/vs_10"
            android:textSize="@dimen/ts_22"  />

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWordFenci"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/llWord"
            android:layout_width="@dimen/vs_200"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="20mm"
            android:paddingBottom="@dimen/vs_20">

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewWord"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingLeft="20mm"
            android:paddingBottom="@dimen/vs_20"
            android:paddingRight="20mm"
            tools:ignore="InOrMmUsage">


            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridViewFilter"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:visibility="invisible"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>