<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.github.tvbox.osc.player">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!--    <application-->
    <!--        android:allowBackup="true"-->
    <!--        android:label="@string/app_name"-->
    <!--        android:supportsRtl="true">-->
    <!--        <activity android:name="tv.danmaku.ijk.media.player.demo.IjkDemoActivity">-->
    <!--            <intent-filter>-->
    <!--                <action android:name="android.intent.action.MAIN" />-->

    <!--                <category android:name="android.intent.category.LAUNCHER" />-->
    <!--            </intent-filter>-->
    <!--        </activity>-->
    <!--    </application>-->

</manifest>