# 本地数据分页功能实现说明

## 功能概述

本次实现为TVBox应用的本地数据模式添加了分页功能，当数据源切换为本地模式时，首页影片将以分页方式展示，每页显示30条数据，并根据总条数生成分页按钮。该功能兼容Android TV应用和智能手机。

## 主要特性

- ✅ **分页显示**: 每页显示30条本地视频数据
- ✅ **自动加载**: 支持下拉加载更多功能
- ✅ **电视优化**: 针对Android TV遥控器导航进行了优化
- ✅ **预加载**: 滚动到倒数第10个项目时自动预加载下一页
- ✅ **焦点管理**: 优化了电视端的焦点切换和动画效果
- ✅ **兼容性**: 同时支持手机和电视端操作

## 实现细节

### 1. LocalDataHelper 增强

在 `LocalDataHelper.java` 中添加了分页支持：

```java
// 新增分页加载方法
public static LocalVideoPageResult loadLocalVideosWithPagination(
    String rootPath, Context context, int page, int pageSize)

// 新增分页结果类
public static class LocalVideoPageResult {
    public List<Movie.Video> videos;
    public int currentPage;
    public int totalPages;
    public int totalCount;
    public int pageSize;
    public boolean hasMore;
}
```

### 2. UserFragment 分页集成

在 `UserFragment.java` 中实现了完整的分页逻辑：

- **分页状态管理**: 添加了页码、加载状态等变量
- **加载监听器**: 为HomeHotVodAdapter设置了LoadMore监听器
- **电视导航优化**: 添加了专门的电视导航处理方法

### 3. 电视端优化功能

- **焦点动画**: 选中项目时的缩放动画效果
- **预加载机制**: 滚动到接近底部时自动加载下一页
- **边界处理**: 优化了遥控器按键的边界导航
- **居中显示**: 选中项目自动居中显示

## 使用方法

### 启用本地数据分页

1. 在应用设置中将数据源切换为"本地模式"
2. 设置本地数据路径
3. 返回首页即可看到分页效果

### 操作说明

**手机端:**
- 向下滑动列表自动加载更多数据
- 点击视频项目进入详情页

**电视端:**
- 使用遥控器方向键导航
- 滚动到底部自动加载下一页
- 按确认键选择视频项目

## 技术架构

### 分页流程

1. **初始化**: 重置分页状态，加载第一页数据
2. **数据加载**: 调用LocalDataHelper的分页方法获取数据
3. **UI更新**: 根据是否为第一页决定替换或追加数据
4. **状态管理**: 更新hasMore状态和页码
5. **加载完成**: 设置适配器的加载状态

### 关键组件

- `LocalDataHelper`: 负责本地视频文件的扫描和分页处理
- `UserFragment`: 管理分页状态和UI交互
- `HomeHotVodAdapter`: 处理数据展示和加载更多
- `TvRecyclerView`: 提供电视端优化的列表控件

## 配置参数

- **每页数量**: 30条（可在代码中调整 `localDataPageSize` 变量）
- **预加载阈值**: 倒数第10个项目（可在 `setupTvNavigationForLocalData` 方法中调整）
- **动画时长**: 300ms（焦点切换动画）

## 兼容性说明

### Android TV 特性

- 支持遥控器方向键导航
- 焦点管理和视觉反馈
- 边界按键事件处理
- 选中项居中显示

### 手机端特性

- 触摸滑动操作
- 下拉刷新支持
- 响应式布局适配

## 测试建议

1. **功能测试**:
   - 验证每页确实显示30条数据
   - 测试加载更多功能
   - 检查最后一页的处理

2. **性能测试**:
   - 大量本地视频文件的加载性能
   - 内存使用情况
   - 滑动流畅度

3. **兼容性测试**:
   - 不同Android版本的兼容性
   - 电视端和手机端的操作体验
   - 不同屏幕尺寸的适配

## 注意事项

- 本地视频文件需要按照应用要求的目录结构组织
- 分页功能仅在本地数据模式（HOME_REC = 3）下生效
- 电视端的导航优化需要TvRecyclerView控件支持
- 建议定期清理缓存以保持最佳性能

## 后续优化建议

1. 添加分页指示器显示当前页码
2. 支持跳转到指定页面
3. 添加搜索和过滤功能
4. 优化大文件夹的扫描性能
5. 添加分页大小的用户配置选项
