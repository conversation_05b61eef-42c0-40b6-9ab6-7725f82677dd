package com.github.tvbox.osc.util;

import android.text.TextUtils;

import java.io.File;
import java.util.Arrays;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地目录扫描缓存工具类
 * 用于缓存目录扫描结果，避免重复的文件系统IO操作
 */
public class LocalDirectoryCacheUtil {
    
    private static final String TAG = "LocalDirectoryCache";
    
    /**
     * 目录扫描结果缓存项
     */
    public static class DirectoryScanResult {
        public File[] subDirectories;
        public long lastModified;
        public long scanTime;
        
        public DirectoryScanResult(File[] subDirectories, long lastModified) {
            this.subDirectories = subDirectories;
            this.lastModified = lastModified;
            this.scanTime = System.currentTimeMillis();
        }
        
        /**
         * 检查缓存是否仍然有效
         */
        public boolean isValid(File rootDir) {
            // 缓存超过5分钟失效
            if (System.currentTimeMillis() - scanTime > 5 * 60 * 1000) {
                return false;
            }
            
            // 目录修改时间变化则失效
            if (rootDir.lastModified() != lastModified) {
                return false;
            }
            
            return true;
        }
    }
    
    // 目录扫描结果缓存
    private static final ConcurrentHashMap<String, DirectoryScanResult> scanCache = new ConcurrentHashMap<>();
    
    /**
     * 获取目录的子目录列表（带缓存）
     * @param rootPath 根目录路径
     * @return 子目录数组，如果目录不存在或无子目录则返回空数组
     */
    public static File[] getSubDirectories(String rootPath) {
        if (TextUtils.isEmpty(rootPath)) {
            return new File[0];
        }
        
        File rootDir = new File(rootPath);
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            return new File[0];
        }
        
        String cacheKey = rootDir.getAbsolutePath();
        
        // 检查缓存
        DirectoryScanResult cachedResult = scanCache.get(cacheKey);
        if (cachedResult != null && cachedResult.isValid(rootDir)) {
            return cachedResult.subDirectories;
        }
        
        // 扫描目录
        File[] subDirs = rootDir.listFiles(file -> file.isDirectory());
        if (subDirs == null) {
            subDirs = new File[0];
        }
        
        // 对目录进行自然排序
        Arrays.sort(subDirs, (f1, f2) -> compareNaturally(f1.getName(), f2.getName()));
        
        // 缓存结果
        DirectoryScanResult result = new DirectoryScanResult(subDirs, rootDir.lastModified());
        scanCache.put(cacheKey, result);
        
        return subDirs;
    }
    
    /**
     * 获取目录总数（带缓存）
     * @param rootPath 根目录路径
     * @return 子目录总数
     */
    public static int getSubDirectoryCount(String rootPath) {
        File[] subDirs = getSubDirectories(rootPath);
        return subDirs.length;
    }
    
    /**
     * 获取指定范围的子目录（带缓存）
     * @param rootPath 根目录路径
     * @param startIndex 起始索引
     * @param endIndex 结束索引（不包含）
     * @return 指定范围的子目录数组
     */
    public static File[] getSubDirectoriesRange(String rootPath, int startIndex, int endIndex) {
        File[] allSubDirs = getSubDirectories(rootPath);
        
        if (startIndex >= allSubDirs.length) {
            return new File[0];
        }
        
        int actualEndIndex = Math.min(endIndex, allSubDirs.length);
        if (startIndex >= actualEndIndex) {
            return new File[0];
        }
        
        return Arrays.copyOfRange(allSubDirs, startIndex, actualEndIndex);
    }
    
    /**
     * 清除指定目录的缓存
     * @param rootPath 根目录路径
     */
    public static void clearCache(String rootPath) {
        if (!TextUtils.isEmpty(rootPath)) {
            File rootDir = new File(rootPath);
            scanCache.remove(rootDir.getAbsolutePath());
        }
    }
    
    /**
     * 清除所有缓存
     */
    public static void clearAllCache() {
        scanCache.clear();
    }
    
    /**
     * 获取缓存信息
     */
    public static String getCacheInfo() {
        return "目录缓存条目: " + scanCache.size();
    }
    
    /**
     * 自然排序工具方法，支持文件名中的数字正确排序
     */
    private static int compareNaturally(String str1, String str2) {
        if (str1 == null && str2 == null) return 0;
        if (str1 == null) return -1;
        if (str2 == null) return 1;
        
        int len1 = str1.length();
        int len2 = str2.length();
        int pos1 = 0, pos2 = 0;
        
        while (pos1 < len1 && pos2 < len2) {
            char c1 = str1.charAt(pos1);
            char c2 = str2.charAt(pos2);
            
            if (Character.isDigit(c1) && Character.isDigit(c2)) {
                // 提取数字部分进行数值比较
                StringBuilder num1 = new StringBuilder();
                StringBuilder num2 = new StringBuilder();
                
                while (pos1 < len1 && Character.isDigit(str1.charAt(pos1))) {
                    num1.append(str1.charAt(pos1++));
                }
                while (pos2 < len2 && Character.isDigit(str2.charAt(pos2))) {
                    num2.append(str2.charAt(pos2++));
                }
                
                try {
                    int n1 = Integer.parseInt(num1.toString());
                    int n2 = Integer.parseInt(num2.toString());
                    if (n1 != n2) {
                        return Integer.compare(n1, n2);
                    }
                } catch (NumberFormatException e) {
                    // 如果数字太大，按字符串比较
                    int cmp = num1.toString().compareTo(num2.toString());
                    if (cmp != 0) return cmp;
                }
            } else {
                // 字符比较
                if (c1 != c2) {
                    return Character.compare(c1, c2);
                }
                pos1++;
                pos2++;
            }
        }
        
        return Integer.compare(len1, len2);
    }
}
