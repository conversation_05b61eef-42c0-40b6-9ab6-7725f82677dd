<?xml version="1.0" encoding="utf-8"?>

<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_focused="true">
        <layer-list>
            <item android:bottom="@dimen/vs_5" android:left="@dimen/vs_20_" android:right="@dimen/vs_20_" android:top="@dimen/vs_20_">
                <shape android:shape="rectangle">
                    <stroke android:width="@dimen/vs_2" android:color="@color/color_CC000000" />
                    <solid android:color="@android:color/transparent" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:state_focused="false">
        <layer-list>
            <item android:bottom="@dimen/vs_5" android:left="@dimen/vs_20_" android:right="@dimen/vs_20_" android:top="@dimen/vs_20_">
                <shape android:shape="rectangle">
                    <stroke android:width="@dimen/vs_1" android:color="@color/color_6C3D3D3D" />
                    <solid android:color="@android:color/transparent" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>