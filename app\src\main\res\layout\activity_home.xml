<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/topLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_10"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/vs_50"
        android:paddingRight="@dimen/vs_50"
        android:paddingBottom="@dimen/vs_10"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="left|center_vertical"
            android:text="@string/app_name"
            android:textAlignment="gravity"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_30"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvDate"
            android:layout_width="@dimen/vs_0"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="right|center_vertical"
            android:textAlignment="gravity"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_28"
            tools:text="111111111111111" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contentLayout"
        android:layout_width="@dimen/vs_0"
        android:layout_height="@dimen/vs_0"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/topLayout">

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginBottom="@dimen/vs_10"
            android:paddingLeft="@dimen/vs_50"
            android:paddingRight="@dimen/vs_50"
            app:tv_selectedItemIsCentered="true" />

        <com.github.tvbox.osc.ui.tv.widget.NoScrollViewPager
            android:id="@+id/mViewPager"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>