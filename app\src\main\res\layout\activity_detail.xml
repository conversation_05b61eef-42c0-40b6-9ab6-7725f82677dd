<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/llLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/topLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/vs_10"
            android:orientation="horizontal"
            android:paddingLeft="@dimen/vs_40"
            android:paddingRight="@dimen/vs_50"
            android:paddingBottom="@dimen/vs_10">

            <ImageView
                android:id="@+id/ivThumb"
                android:layout_width="@dimen/vs_240"
                android:layout_height="@dimen/vs_320"
                android:scaleType="fitXY" />

            <View
                android:id="@+id/previewPlayerPlace"
                android:layout_width="@dimen/vs_480"
                android:layout_height="@dimen/vs_320"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_320"
                android:layout_marginStart="@dimen/vs_20"
                android:layout_marginLeft="@dimen/vs_20"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:focusable="false"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_28" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvSite"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:focusable="false"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_CCFFFFFF"
                        android:textSize="@dimen/ts_20" />

                    <TextView
                        android:id="@+id/tvYear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:focusable="false"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_CCFFFFFF"
                        android:textSize="@dimen/ts_20" />

                    <TextView
                        android:id="@+id/tvArea"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:focusable="false"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_CCFFFFFF"
                        android:textSize="@dimen/ts_20" />

                    <TextView
                        android:id="@+id/tvLang"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:focusable="false"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_CCFFFFFF"
                        android:textSize="@dimen/ts_20" />

                    <TextView
                        android:id="@+id/tvType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:focusable="false"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:singleLine="true"
                        android:textColor="@color/color_CCFFFFFF"
                        android:textSize="@dimen/ts_20" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tvActor"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:focusable="false"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_20" />

                <TextView
                    android:id="@+id/tvDirector"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/vs_5"
                    android:focusable="false"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_20" />

                <TextView
                    android:id="@+id/tvPlayUrl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:focusable="false"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_20" />


                <TextView
                    android:id="@+id/tvDes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:ellipsize="end"
                    android:lineSpacingMultiplier="1.2"
                    android:maxLines="3"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_20" />

                <Space
                    android:layout_width="wrap_content"
                    android:layout_height="0mm"
                    android:layout_weight="1" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvPlay"
                        android:layout_width="@dimen/vs_120"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_play"
                        android:focusable="true"
                        android:nextFocusUp="@id/previewPlayerBlock"
                        android:gravity="center"
                        android:text="立即播放"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvQuickSearch"
                        android:layout_width="@dimen/vs_120"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_quick_search"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="快速搜索"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvDesc"
                        android:layout_width="@dimen/vs_120"
                        android:layout_height="@dimen/vs_40"
                        android:layout_marginEnd="@dimen/vs_10"
                        android:layout_marginRight="@dimen/vs_10"
                        android:background="@drawable/button_detail_sort"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="简介"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />

                    <TextView
                        android:id="@+id/tvCollect"
                        android:layout_width="@dimen/vs_120"
                        android:layout_height="@dimen/vs_40"
                        android:background="@drawable/button_detail_collect"
                        android:focusable="true"
                        android:gravity="center"
                        android:text="加入收藏"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/vs_20" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:id="@+id/mEmptyPlaylist"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            tools:visibility="gone">

            <ImageView
                android:layout_width="@dimen/vs_128"
                android:layout_height="@dimen/vs_128"
                android:layout_gravity="center"
                android:src="@drawable/icon_empty" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/vs_10"
                android:layout_marginLeft="@dimen/vs_10"
                android:gravity="center"
                android:text="暂无播放数据"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_24" />
        </LinearLayout>

        <!-- mGridViewFlag 区域 -->
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewFlag"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vs_45"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_0"
            android:layout_marginRight="@dimen/vs_50"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:focusable="true"
            android:nextFocusUp="@id/previewPlayerPlace"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true" />

        <LinearLayout
            android:id="@+id/mSeriesGroupTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:gravity="center_vertical"
            android:focusable="false"
            android:visibility="gone">

            <!-- 排序功能按钮 -->
            <TextView
                android:id="@+id/mSeriesSortTv"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_45"
                android:background="@drawable/shape_source_series_focus"
                android:gravity="center"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:text="正序"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"
                android:ellipsize="marquee"
                android:singleLine="true"
                android:focusable="true"
                android:nextFocusUp="@id/mGridViewFlag"
                android:nextFocusDown="@id/mGridView"
                android:nextFocusRight="@id/mSeriesGroupView"/>

            <!-- 原来的 mSeriesGroupView -->
            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mSeriesGroupView"
                android:layout_width="0dp"
                android:layout_height="@dimen/vs_45"
                android:layout_weight="1"
                android:layout_marginLeft="@dimen/vs_10"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
                app:tv_selectedItemIsCentered="true"
                android:focusable="true"
                android:nextFocusUp="@id/mGridViewFlag"
                android:nextFocusDown="@id/mGridView"
                android:nextFocusLeft="@id/mSeriesSortTv"/>
        </LinearLayout>


        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/vs_50"
            android:layout_marginTop="@dimen/vs_10"
            android:layout_marginRight="@dimen/vs_50"
            android:layout_marginBottom="@dimen/vs_10"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_10" />
    </LinearLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/previewPlayer"
        android:layout_width="@dimen/vs_470"
        android:layout_height="@dimen/vs_300"
        android:layout_marginStart="@dimen/vs_50"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_20"
        android:layout_marginBottom="@dimen/vs_10"
        android:visibility="gone" />

    <View
        android:id="@+id/previewPlayerBlock"
        android:layout_width="@dimen/vs_470"
        android:layout_height="@dimen/vs_300"
        android:focusable="true"
        android:nextFocusDown="@id/mGridViewFlag"
        android:background="@drawable/preview_player_block"
        android:layout_marginStart="@dimen/vs_50"
        android:layout_marginLeft="@dimen/vs_50"
        android:layout_marginTop="@dimen/vs_20"
        android:layout_marginBottom="@dimen/vs_10"
        android:visibility="gone" />
</FrameLayout>
