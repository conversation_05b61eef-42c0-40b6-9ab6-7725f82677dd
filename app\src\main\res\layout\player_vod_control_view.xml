<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <LinearLayout
            android:id="@+id/tv_top_l_container"
            android:layout_width="0dp"
            android:layout_weight="3"
            android:layout_height="wrap_content"
            android:tag="tv_top_container"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginStart="@dimen/vs_10"
            android:layout_marginLeft="@dimen/vs_10"
            android:layout_marginTop="@dimen/vs_5"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_info_name1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:ellipsize="end"
                android:singleLine="true"
                android:paddingTop="@dimen/vs_5"
                android:paddingLeft="@dimen/vs_10"
                android:text="@string/http"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"
                android:paddingStart="@dimen/vs_10"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

            <TextView
                android:id="@+id/tv_videosize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:tag="vod_video_size"
                android:text="@string/_1024_x_768"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"
                android:paddingStart="@dimen/vs_10"
                tools:ignore="RtlHardcoded,RtlSymmetry" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/tv_top_r_container"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:tag="tv_top_container"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_gravity="right"
            android:layout_marginRight="@dimen/vs_10"
            android:layout_marginTop="@dimen/vs_5"
            android:layout_marginEnd="@dimen/vs_10"
            android:orientation="vertical"
            tools:ignore="RtlHardcoded">
            <LinearLayout
                android:layout_width="match_parent"
                android:id="@+id/tv_screen_display"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="right">

                <TextView
                    android:id="@+id/net_play_speed"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingTop="@dimen/vs_5"
                    android:paddingRight="@dimen/vs_10"
                    android:tag="vod_control_pause_time"
                    android:text="0Gbs"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20"
                    tools:ignore="RtlSymmetry" />

                <TextView
                    android:id="@+id/tv_seek_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingTop="@dimen/vs_5"
                    android:paddingRight="@dimen/vs_10"
                    android:tag="vod_control_pause_time"
                    android:text="@string/_00_00_00_00"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20"
                    android:paddingEnd="@dimen/vs_10"
                    tools:ignore="RtlSymmetry" />

                <TextView
                    android:id="@+id/tv_sys_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:paddingTop="@dimen/vs_5"
                    android:paddingRight="@dimen/vs_10"
                    android:tag="vod_control_pause_time"
                    android:text="@string/_00_00_00"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20"
                    tools:ignore="RtlSymmetry" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_play_load_net_speed_right_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:ellipsize="end"
                android:maxLines="1"
                android:paddingRight="@dimen/vs_10"
                android:tag="vod_control_pause_time"
                android:text="@string/_0kb_s"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"
                tools:ignore="RtlSymmetry" />
        </LinearLayout>
    </LinearLayout>

    <com.github.tvbox.osc.subtitle.widget.SimpleSubtitleView
        android:id="@+id/subtitle_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:gravity="center"
        android:paddingLeft="@dimen/vs_20"
        android:paddingTop="@dimen/vs_15"
        android:paddingRight="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_15"
        android:text=""
        android:textColor="#ffffff"
        android:textSize="16sp"
        android:textStyle="bold"/>

    <LinearLayout
        android:id="@+id/bottom_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/shape_dialog_filter_bg"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:paddingLeft="@dimen/vs_20"
        android:paddingTop="@dimen/vs_20"
        android:paddingRight="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_10"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/play_btn_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10"
            android:orientation="horizontal">

            <TextView
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:paddingRight="@dimen/vs_10"
                android:text="播放"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_next"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="下一集"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_pre"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="上一集"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_retry"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="重播"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_refresh"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="刷新"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_scale"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="16:9"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_speed"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="x1.0"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_player"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="系统播放器"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_ijk"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="硬解码"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_time_start_end_text"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:padding="@dimen/vs_10"
                android:text="片头尾"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_time_start"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="01:00"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/play_time_end"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="01:00"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <!--添加片头片尾重置按钮-->
            <TextView
                android:id="@+id/play_time_reset"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="重置"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/zimu_select"
                android:singleLine="true"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:text="字幕"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <TextView
                android:id="@+id/audio_track_select"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:visibility="gone"
                android:text="音轨"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"/>

            <TextView
                android:id="@+id/landscape_portrait"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:visibility="gone"
                android:text="横竖屏"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"/>
            <TextView
                android:id="@+id/screen_display"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:background="@drawable/button_dialog_main"
                android:focusable="true"
                android:padding="@dimen/vs_10"
                android:visibility="visible"
                android:text="屏显"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20"/>

        </LinearLayout>


        <LinearLayout
            android:id="@+id/parse_root"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10"
            android:orientation="horizontal"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginEnd="@dimen/vs_5"
                android:layout_marginRight="@dimen/vs_5"
                android:paddingRight="@dimen/vs_10"
                android:text="解析"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_20" />

            <com.owen.tvrecyclerview.widget.TvRecyclerView
                android:id="@+id/mGridParseView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
                app:tv_selectedItemIsCentered="true"
                app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10"
            android:orientation="horizontal"
            android:baselineAligned="false"
            android:gravity="center_vertical">
            <TextView
                android:id="@+id/curr_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:minWidth="48dp"
                android:paddingStart="@dimen/vs_0"
                android:paddingEnd="@dimen/vs_0"
                android:text="@string/_00_00"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_20"
                android:gravity="end|center_vertical"
                android:textAllCaps="true"
                android:includeFontPadding="false"
                tools:ignore="RtlSymmetry" />

            <SeekBar
                android:id="@+id/seekBar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="16"
                android:layout_gravity="center_vertical"
                android:max="1000"
                android:progressDrawable="@drawable/shape_player_control_vod_seek"
                android:thumb="@drawable/shape_player_control_vod_seek_thumb"
                android:background="@null"
                android:maxHeight="@dimen/vs_6"
                android:minHeight="@dimen/vs_6"
                android:paddingVertical="@dimen/vs_2"
                android:focusable="false"
                android:focusableInTouchMode="false" />

            <TextView
                android:id="@+id/total_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:minWidth="48dp"
                android:paddingStart="@dimen/vs_0"
                android:paddingEnd="@dimen/vs_0"
                android:text="@string/_00_00"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_20"
                android:gravity="start|center_vertical"
                android:includeFontPadding="false"
                tools:ignore="RtlSymmetry" />
        </LinearLayout>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/tv_pause_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:tag="vod_control_pause"
        android:visibility="gone"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="left"
            android:layout_marginStart="@dimen/vs_20"
            android:layout_marginLeft="@dimen/vs_20"
            android:layout_marginTop="@dimen/vs_10"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_info_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:maxLines="3"
                android:ellipsize="end"
                android:paddingTop="@dimen/vs_20"
                android:paddingLeft="@dimen/vs_20"
                android:text="http://"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="@dimen/vs_280"
            android:layout_height="@dimen/vs_200"
            android:layout_gravity="center"
            android:background="@drawable/shape_user_focus"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/vs_60"
                android:layout_height="@dimen/vs_60"
                android:layout_gravity="center"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:src="@drawable/icon_play" />

            <TextView
                android:id="@+id/tv_pause_progress_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/vs_20"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:tag="vod_control_pause_t"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_30"
                tools:text="100" />
        </LinearLayout>

    </FrameLayout>

    <TextView
        android:id="@+id/tv_slide_progress_text"
        android:layout_width="@dimen/vs_200"
        android:layout_height="@dimen/vs_100"
        android:layout_gravity="center"
        android:background="@drawable/shape_user_focus"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:tag="vod_control_slide_info"
        android:textAlignment="gravity"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_30"
        android:visibility="gone"
        tools:text="100" />

    <LinearLayout
        android:id="@+id/tv_progress_container"
        android:layout_width="@dimen/vs_280"
        android:layout_height="@dimen/vs_200"
        android:layout_gravity="center"
        android:background="@drawable/shape_user_focus"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:id="@+id/tv_progress_icon"
            android:layout_width="@dimen/vs_60"
            android:layout_height="@dimen/vs_60"
            android:focusable="false"
            android:focusableInTouchMode="false"
            tools:src="@drawable/icon_back" />

        <TextView
            android:id="@+id/tv_progress_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_20"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_30"
            tools:text="100" />

    </LinearLayout>

    <ProgressBar
        android:layout_width="@dimen/vs_50"
        android:layout_height="@dimen/vs_50"
        android:layout_gravity="center"
        android:indeterminateBehavior="repeat"
        android:indeterminateDrawable="@drawable/anim_loading"
        android:indeterminateOnly="true"
        android:tag="vod_control_loading"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_play_load_net_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/vs_40"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:tag="play_load_net_speed"
        android:text="0"
        android:textColor="@color/color_FFFFFF"
        android:textSize="@dimen/ts_20" />

    <ImageView
        android:id="@+id/tv_back"
        android:layout_width="@dimen/vs_80"
        android:layout_height="@dimen/vs_80"
        android:layout_gravity="left|center"
        android:clickable="true"
        android:focusable="true"
        android:paddingHorizontal="@dimen/vs_20"
        android:src="@drawable/icon_back"
        android:visibility="invisible" />


    <ImageView
        android:id="@+id/tv_lock"
        android:layout_width="@dimen/vs_80"
        android:layout_height="@dimen/vs_80"
        android:layout_gravity="right|center"
        android:clickable="true"
        android:focusable="true"
        android:paddingHorizontal="@dimen/vs_20"
        android:src="@drawable/icon_unlock"
        android:visibility="invisible" />

    <FrameLayout
        android:id="@+id/play_speed_3_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/bg_speed_indicator"
        android:visibility="gone"
        android:padding="8dp">

        <TextView
            android:id="@+id/tv_speed_3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="3.0 X"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold" />
    </FrameLayout>

</FrameLayout>
