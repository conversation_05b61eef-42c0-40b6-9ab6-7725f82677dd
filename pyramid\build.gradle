plugins {
    id 'com.android.library'
    id 'com.chaquo.python'
}

android {
    compileSdk 33

    defaultConfig {
        minSdkVersion 19
        targetSdkVersion 28
        ndk {
            //abiFilters 'arm64-v8a'
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a"
        }
        python {
            buildPython("D:/Programs/Python/Python38/python.exe")
            pip {
                install "lxml"
                install "ujson"
                install "pyquery"
                install "requests"
                install "jsonpath"
                install "cachetools"
                install 'pycryptodome'
                install 'beautifulsoup4'
            }
        }
    }

    buildTypes {
        all {
            ndk {
                //noinspection ChromeOsAbiSupport
                abiFilters 'armeabi-v7a'
            }
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main{
            // Android代码模块
            setRoot "src/main"
            // Python代码模块，也就是你的Python源码所在项目中的文件夹
            python.srcDirs = ["src/python"]
        }
    }
    buildToolsVersion '30.0.3'
}

dependencies {

}