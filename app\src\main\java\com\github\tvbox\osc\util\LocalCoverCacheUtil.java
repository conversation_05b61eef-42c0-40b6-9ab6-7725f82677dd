package com.github.tvbox.osc.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import com.github.tvbox.osc.base.App;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.List;
import java.util.ArrayList;

/**
 * 本地影片封面缓存工具类
 * 用于缓存网络封面图片到本地，避免重复请求
 * 只使用磁盘缓存
 */
public class LocalCoverCacheUtil {
    
    private static final String CACHE_DIR_NAME = "local_covers";
    private static final String TAG = "LocalCoverCache";
    
    // 正在下载的URL集合，避免重复下载
    private static final ConcurrentHashMap<String, List<CacheCallback>> downloadingUrls = new ConcurrentHashMap<>();
    
    // 线程池用于异步缓存
    private static final ExecutorService executor = Executors.newFixedThreadPool(3);
    
    /**
     * 获取缓存目录
     */
    private static File getCacheDir() {
        File cacheDir = new File(App.getInstance().getCacheDir(), CACHE_DIR_NAME);
        if (!cacheDir.exists()) {
            cacheDir.mkdirs();
        }
        return cacheDir;
    }
    
    /**
     * 将URL转换为文件名（使用MD5）
     */
    private static String urlToFileName(String url) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(url.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString() + ".png";
        } catch (NoSuchAlgorithmException e) {
            // 如果MD5不可用，使用hashCode
            return String.valueOf(url.hashCode()) + ".png";
        }
    }
    
    /**
     * 获取缓存文件路径
     */
    private static File getCacheFile(String url) {
        return new File(getCacheDir(), urlToFileName(url));
    }
    
    /**
     * 检查本地是否已缓存该URL的图片
     */
    public static boolean isCached(String url) {
        if (TextUtils.isEmpty(url) || !url.startsWith("http")) {
            return false;
        }
        
        // 检查磁盘缓存
        File cacheFile = getCacheFile(url);
        return cacheFile.exists() && cacheFile.length() > 0;
    }
    
    /**
     * 获取缓存的封面图片（本地文件路径）
     */
    public static String getCachedCover(String url) {
        if (TextUtils.isEmpty(url) || !url.startsWith("http")) {
            return url; // 如果不是网络URL，直接返回
        }
        
        // 从磁盘缓存获取
        File cacheFile = getCacheFile(url);
        if (cacheFile.exists() && cacheFile.length() > 0) {
            return cacheFile.getAbsolutePath();
        }
        
        return url; // 如果没有缓存，返回原URL
    }
    
    /**
     * 异步缓存网络封面图片
     */
    public static void cacheNetworkCover(String url, CacheCallback callback) {
        if (TextUtils.isEmpty(url) || !url.startsWith("http")) {
            if (callback != null) {
                callback.onResult(url);
            }
            return;
        }
        
        // 如果已经缓存，直接返回
        if (isCached(url)) {
            String cachedPath = getCachedCover(url);
            if (callback != null) {
                callback.onResult(cachedPath);
            }
            return;
        }
        
        // 如果正在下载，将回调加入队列
        List<CacheCallback> callbacks = downloadingUrls.get(url);
        if (callbacks != null) {
            if (callback != null) {
                synchronized (callbacks) {
                    callbacks.add(callback);
                }
            }
            return;
        }
        
        // 创建回调队列并标记为正在下载
        callbacks = new ArrayList<>();
        if (callback != null) {
            callbacks.add(callback);
        }
        downloadingUrls.put(url, callbacks);
        
        // 使用Picasso下载图片
        Target target = new Target() {
            @Override
            public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                executor.execute(() -> {
                    try {
                        // 保存到文件缓存
                        File cacheFile = getCacheFile(url);
                        saveBitmapToFile(bitmap, cacheFile);
                        
                        String cachePath = cacheFile.getAbsolutePath();
                        
                        LOG.i(TAG + ": 封面缓存成功: " + url);
                        
                        // 通知所有等待的回调
                        List<CacheCallback> waitingCallbacks = downloadingUrls.remove(url);
                        if (waitingCallbacks != null) {
                            for (CacheCallback cb : waitingCallbacks) {
                                if (cb != null) {
                                    cb.onResult(cachePath);
                                }
                            }
                        }
                    } catch (Exception e) {
                        LOG.e(TAG + ": 保存封面缓存失败: " + e.getMessage());
                        // 通知所有等待的回调
                        List<CacheCallback> waitingCallbacks = downloadingUrls.remove(url);
                        if (waitingCallbacks != null) {
                            for (CacheCallback cb : waitingCallbacks) {
                                if (cb != null) {
                                    cb.onResult(url);
                                }
                            }
                        }
                    }
                });
            }
            
            @Override
            public void onBitmapFailed(Exception e, android.graphics.drawable.Drawable errorDrawable) {
                LOG.e(TAG + ": 下载封面失败: " + url + ", " + (e != null ? e.getMessage() : "未知错误"));
                // 通知所有等待的回调
                List<CacheCallback> waitingCallbacks = downloadingUrls.remove(url);
                if (waitingCallbacks != null) {
                    for (CacheCallback cb : waitingCallbacks) {
                        if (cb != null) {
                            cb.onResult(url);
                        }
                    }
                }
            }
            
            @Override
            public void onPrepareLoad(android.graphics.drawable.Drawable placeHolderDrawable) {
                // 开始加载
            }
        };
        
        try {
            Picasso.get()
                .load(url)
                .config(Bitmap.Config.RGB_565) // 使用较小的颜色配置节省内存
                .into(target);
        } catch (Exception e) {
            LOG.e(TAG + ": 启动图片下载失败: " + e.getMessage());
            List<CacheCallback> waitingCallbacks = downloadingUrls.remove(url);
            if (waitingCallbacks != null) {
                for (CacheCallback cb : waitingCallbacks) {
                    if (cb != null) {
                        cb.onResult(url);
                    }
                }
            }
        }
    }
    
    /**
     * 将Bitmap保存到文件
     */
    private static void saveBitmapToFile(Bitmap bitmap, File file) throws IOException {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos);
            fos.flush();
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    // ignore
                }
            }
        }
    }
    
    /**
     * 清理过期的缓存文件（可选择性调用）
     */
    public static void cleanExpiredCache(long maxAgeMillis) {
        File cacheDir = getCacheDir();
        File[] files = cacheDir.listFiles();
        if (files == null) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        int deletedCount = 0;
        
        for (File file : files) {
            if (file.isFile() && (currentTime - file.lastModified()) > maxAgeMillis) {
                if (file.delete()) {
                    deletedCount++;
                }
            }
        }
        
        if (deletedCount > 0) {
            LOG.i(TAG + ": 清理过期缓存文件: " + deletedCount + " 个");
        }
    }
    
    /**
     * 清理所有缓存
     */
    public static void clearAllCache() {
        // 清理文件缓存
        File cacheDir = getCacheDir();
        File[] files = cacheDir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    file.delete();
                }
            }
        }
        
        LOG.i(TAG + ": 已清理所有封面缓存");
    }
    
    /**
     * 获取缓存大小（字节）
     */
    public static long getCacheSize() {
        File cacheDir = getCacheDir();
        File[] files = cacheDir.listFiles();
        if (files == null) {
            return 0;
        }
        
        long totalSize = 0;
        for (File file : files) {
            if (file.isFile()) {
                totalSize += file.length();
            }
        }
        
        return totalSize;
    }
    
    /**
     * 缓存回调接口
     */
    public interface CacheCallback {
        void onResult(String result); // result可能是本地文件路径或原始URL
    }
} 