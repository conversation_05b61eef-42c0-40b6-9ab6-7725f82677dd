package com.github.tvbox.osc.ui.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.github.tvbox.osc.R;
import com.github.tvbox.osc.util.DeviceUtils;

import org.jetbrains.annotations.NotNull;

public class ActivationDialog extends BaseDialog {
    private EditText etActivationCode;
    private TextView btnActivate;
    private TextView btnCancel;
    private OnActivationListener listener;

    public ActivationDialog(@NonNull @NotNull Context context) {
        super(context);
        setContentView(R.layout.dialog_activation);
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        initViews();
    }

    private void initViews() {
        etActivationCode = findViewById(R.id.etActivationCode);
        btnActivate = findViewById(R.id.btnActivate);
        btnCancel = findViewById(R.id.btnCancel);
        
        // 如果是智能电视，设置焦点管理
        if (DeviceUtils.shouldUseTVFocusManagement(getContext())) {
            setupTVFocusManagement();
        }

        btnActivate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String activationCode = etActivationCode.getText().toString().trim();
                if (activationCode.isEmpty()) {
                    Toast.makeText(getContext(), "请输入激活码", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (listener != null) {
                    listener.onActivate(activationCode);
                }
            }
        });

        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onCancel();
                }
            }
        });

        etActivationCode.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_DONE || 
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    btnActivate.performClick();
                    return true;
                }
                return false;
            }
        });

        setOnKeyListener(new OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    if (listener != null) {
                        listener.onCancel();
                    }
                    return true;
                }
                return false;
            }
        });
    }

    public void setOnActivationListener(OnActivationListener listener) {
        this.listener = listener;
    }

    public void setActivating(boolean isActivating) {
        btnActivate.setEnabled(!isActivating);
        btnActivate.setText(isActivating ? "激活中..." : "激活");
        etActivationCode.setEnabled(!isActivating);
    }


    
    /**
     * 设置智能电视的焦点管理
     */
    private void setupTVFocusManagement() {
        // 设置输入框和按钮的焦点属性
        etActivationCode.setFocusable(true);
        etActivationCode.setFocusableInTouchMode(false); // TV环境下不需要触摸模式焦点
        btnActivate.setFocusable(true);
        btnActivate.setFocusableInTouchMode(false);
        btnCancel.setFocusable(true);
        btnCancel.setFocusableInTouchMode(false);
        
        // 设置焦点导航顺序
        etActivationCode.setNextFocusDownId(R.id.btnActivate);
        btnActivate.setNextFocusUpId(R.id.etActivationCode);
        btnActivate.setNextFocusRightId(R.id.btnCancel);
        btnCancel.setNextFocusUpId(R.id.etActivationCode);
        btnCancel.setNextFocusLeftId(R.id.btnActivate);
        
        // 延迟设置默认焦点到输入框，确保布局完成
        etActivationCode.post(new Runnable() {
            @Override
            public void run() {
                etActivationCode.requestFocus();
                // 在TV环境下显示操作提示
                // Toast.makeText(getContext(), "使用遥控器方向键导航，确认键选择", Toast.LENGTH_LONG).show();
            }
        });
    }

    public interface OnActivationListener {
        void onActivate(String activationCode);
        void onCancel();
    }
} 