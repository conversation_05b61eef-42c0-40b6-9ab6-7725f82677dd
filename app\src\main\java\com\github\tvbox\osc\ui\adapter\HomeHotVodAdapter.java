package com.github.tvbox.osc.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.picasso.RoundTransformation;
import com.github.tvbox.osc.util.DefaultConfig;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.ImgUtil;
import com.github.tvbox.osc.util.MD5;
import com.github.tvbox.osc.util.LocalCoverCacheUtil;
import com.github.tvbox.osc.util.LocalImageCacheUtil;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.io.File;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class HomeHotVodAdapter extends BaseQuickAdapter<Movie.Video, BaseViewHolder> {
    private int defaultWidth;
    private final ImgUtil.Style style;
    private String  tvRateValue;

    /**
     * style 数据结构：ratio 指定宽高比（宽 / 高），type 表示风格（例如 rect、list）
     */
    public HomeHotVodAdapter(ImgUtil.Style style,String tvRate) {
        super(R.layout.item_user_hot_vod, new ArrayList<>());
        if(style!=null){
            this.defaultWidth=ImgUtil.getStyleDefaultWidth(style);
        }
        this.style=style;
        this.tvRateValue=tvRate;
    }

    @Override
    protected void convert(BaseViewHolder helper, Movie.Video item) {
        FrameLayout tvDel = helper.getView(R.id.delFrameLayout);
        if (HawkConfig.hotVodDelete) {
            tvDel.setVisibility(View.VISIBLE);
        } else {
            tvDel.setVisibility(View.GONE);
        }

        TextView tvRate = helper.getView(R.id.tvRate);
        if (Hawk.get(HawkConfig.HOME_REC, 0) == 2){
            SourceBean bean =  ApiConfig.get().getSource(item.sourceKey);
            if(bean!=null){
                tvRateValue=bean.getName();
            }else {
                tvRateValue="搜";
            }
        }
        tvRate.setText(tvRateValue);

        TextView tvNote = helper.getView(R.id.tvNote);
        if (item.note == null || item.note.isEmpty()) {
            tvNote.setVisibility(View.GONE);
        } else {
            tvNote.setText(item.note);
            tvNote.setVisibility(View.VISIBLE);
        }
        helper.setText(R.id.tvName, item.name);
        ImageView ivThumb = helper.getView(R.id.ivThumb);

        int newWidth = ImgUtil.defaultWidth;
        int newHeight = ImgUtil.defaultHeight;
        if(style!=null){
            newWidth = defaultWidth;
            newHeight = (int)(newWidth / style.ratio);
        }

        //由于部分电视机使用glide报错
        if (!TextUtils.isEmpty(item.pic)) {
            item.pic=item.pic.trim();
            if(ImgUtil.isBase64Image(item.pic)){
                // 如果是 Base64 图片，解码并设置
                ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(item.pic));
            } else if (item.pic.startsWith("file://") && "local".equals(item.sourceKey)) {
                // 本地图片文件，异步加载Base64
                handleLocalImageFile(item.pic, item.name, ivThumb, newWidth, newHeight);
            } else if (item.pic.startsWith("http") && "local".equals(item.sourceKey)) {
                // 本地影片的网络封面，使用缓存工具类
                String cachedPic = LocalCoverCacheUtil.getCachedCover(item.pic);
                if (!cachedPic.equals(item.pic) && new File(cachedPic).exists()) {
                    // 已缓存到本地文件，使用文件路径加载
                    Picasso.get()
                            .load(new File(cachedPic))
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext, newWidth), AutoSizeUtils.mm2px(mContext, newHeight))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(ImgUtil.createTextDrawable(item.name))
                            .into(ivThumb);
                } else {
                    // 还未缓存，先显示占位图，然后异步缓存并更新
                    // ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                    final String finalUrl = item.pic;
                    final String finalName = item.name;
                    final ImageView finalIvThumb = ivThumb;
                    final int finalNewWidth = newWidth;
                    final int finalNewHeight = newHeight;
                    LocalCoverCacheUtil.cacheNetworkCover(item.pic, new LocalCoverCacheUtil.CacheCallback() {
                        @Override
                        public void onResult(String result) {
                            if (!result.equals(finalUrl) && new File(result).exists()) {
                                // 缓存完成，更新显示
                                if (finalIvThumb.getContext() instanceof android.app.Activity) {
                                    ((android.app.Activity) finalIvThumb.getContext()).runOnUiThread(() -> {
                                        Picasso.get()
                                                .load(new File(result))
                                                .transform(new RoundTransformation(MD5.string2MD5(finalUrl))
                                                        .centerCorp(true)
                                                        .override(AutoSizeUtils.mm2px(mContext, finalNewWidth), AutoSizeUtils.mm2px(mContext, finalNewHeight))
                                                        .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                                .noFade()
                                                .error(ImgUtil.createTextDrawable(finalName))
                                                .into(finalIvThumb);
                                    });
                                }
                            }
                        }
                    });
                }
            }else {
                Picasso.get()
                        .load(DefaultConfig.checkReplaceProxy(item.pic))
                        .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                .centerCorp(true)
                                .override(AutoSizeUtils.mm2px(mContext, newWidth), AutoSizeUtils.mm2px(mContext, newHeight))
                                .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                        .placeholder(R.drawable.img_loading_placeholder)
                        .noFade()
                        .error(ImgUtil.createTextDrawable(item.name))
                        .into(ivThumb);
            }
        } else {
            ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
        }
        applyStyleToImage(ivThumb);//动态设置宽高
    }
    /**
     * 根据传入的 style 动态设置 ImageView 的高度：高度 = 宽度 / ratio
     */
    private void applyStyleToImage(final ImageView ivThumb) {
        if(style!=null){
            ViewGroup container = (ViewGroup) ivThumb.getParent();
            int width = defaultWidth;
            int height = (int) (width / style.ratio);
            ViewGroup.LayoutParams containerParams = container.getLayoutParams();
            containerParams.height = AutoSizeUtils.mm2px(mContext, height); // 高度
            containerParams.width = AutoSizeUtils.mm2px(mContext, width); // 宽度
            container.setLayoutParams(containerParams);
        }
    }

    /**
     * 处理本地图片文件的异步加载
     */
    private void handleLocalImageFile(String filePath, String itemName, ImageView ivThumb, int newWidth, int newHeight) {
        // 先显示占位图
        ivThumb.setImageDrawable(ImgUtil.createTextDrawable(itemName));

        // 提取文件路径
        String actualPath = filePath.substring("file://".length());
        File imageFile = new File(actualPath);

        if (!imageFile.exists()) {
            return;
        }

        // 异步加载Base64
        LocalImageCacheUtil.getBase64Async(imageFile, new LocalImageCacheUtil.Base64Callback() {
            @Override
            public void onResult(String base64Data) {
                // 在主线程中更新UI
                if (ivThumb.getContext() instanceof android.app.Activity) {
                    ((android.app.Activity) ivThumb.getContext()).runOnUiThread(() -> {
                        if (!TextUtils.isEmpty(base64Data)) {
                            ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(base64Data));
                        }
                    });
                }
            }

            @Override
            public void onError(String error) {
                // 加载失败，保持占位图
                // 可以在这里记录日志或进行其他处理
            }
        });
    }
}