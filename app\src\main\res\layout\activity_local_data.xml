<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:scrollbars="vertical">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/vs_30">

    <!-- 标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="@dimen/vs_25_">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="本地数据设置"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_34"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="@dimen/vs_15" />

        <!-- <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="选择包含影片的目录，每个子目录将被识别为一部影片"
            android:textColor="@color/color_BBFFFFFF"
            android:textSize="@dimen/ts_22"
            android:gravity="center" /> -->

        <TextView
            android:id="@+id/tvDeviceType"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设备类型：检测中..."
            android:textColor="@color/color_02F8E1"
            android:textSize="@dimen/ts_16"
            android:gravity="center"
            android:layout_marginTop="@dimen/vs_10" />

    </LinearLayout>

    <!-- 当前路径显示区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="@dimen/vs_30"
        android:background="@drawable/shape_user_focus"
        android:padding="@dimen/vs_15">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="当前目录"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_20"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/vs_8" />

        <TextView
            android:id="@+id/tvCurrentPath"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="未选择目录"
            android:textColor="@color/color_02F8E1"
            android:textSize="@dimen/ts_16"
            android:lineSpacingMultiplier="1.2" />

    </LinearLayout>

    <!-- 操作按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginBottom="@dimen/vs_30">

        <Button
            android:id="@+id/btnSelectPath"
            android:layout_width="0dp"
            android:layout_height="@dimen/vs_60"
            android:layout_weight="1"
            android:text="选择目录"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22"
            android:textStyle="bold"
            android:background="@drawable/shape_button_transparent"
            android:layout_marginRight="@dimen/vs_15" />

        <Button
            android:id="@+id/btnConfirm"
            android:layout_width="0dp"
            android:layout_height="@dimen/vs_60"
            android:layout_weight="1"
            android:text="确认设置"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22"
            android:textStyle="bold"
            android:background="@drawable/shape_button_transparent" />

    </LinearLayout>

    <!-- 说明区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/shape_user_focus"
        android:padding="@dimen/vs_15">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="使用说明"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_22"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/vs_10" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="1. 每个子目录将被识别为一部影片"
            android:textColor="@color/color_BBFFFFFF"
            android:textSize="@dimen/ts_16"
            android:layout_marginBottom="@dimen/vs_6" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="2. 子目录中的 cover.png 文件将作为封面图片"
            android:textColor="@color/color_BBFFFFFF"
            android:textSize="@dimen/ts_16"
            android:layout_marginBottom="@dimen/vs_6" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="3. 支持的视频格式：mp4, mkv, avi, mov, wmv, flv, 3gp, m4v, webm, ts, m3u8"
            android:textColor="@color/color_BBFFFFFF"
            android:textSize="@dimen/ts_16" />

    </LinearLayout>

    <!-- 底部空间 -->
    <Space
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_30" />

</LinearLayout>
</ScrollView> 