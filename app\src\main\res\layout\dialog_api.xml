<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <!-- 将外层容器高度改为 wrap_content，避免内部控件溢出 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_bg_main"
        android:orientation="horizontal"
        android:padding="@dimen/vs_30">

        <ImageView
            android:id="@+id/ivQRCode"
            android:layout_width="@dimen/vs_300"
            android:layout_height="@dimen/vs_300"
            android:layout_gravity="center"
            android:focusable="false" />

        <LinearLayout
            android:layout_width="@dimen/vs_520"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/vs_20"
            android:orientation="vertical"
            android:paddingTop="@dimen/vs_20"
            android:paddingBottom="@dimen/vs_20"
            android:layout_marginLeft="@dimen/vs_20">

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_10"
                android:lineSpacingMultiplier="1.5"
                android:textColor="@color/color_CC000000"
                android:textSize="@dimen/ts_24"
                tools:text="1111111111111111111111111111111111111111111111111111" />

            <!-- 配置历史与存储权限 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/vs_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/apiHistory"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="历史直播"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22"
                    android:layout_marginRight="@dimen/vs_5" />

                <TextView
                    android:id="@+id/storagePermission"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="存储权限"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22"
                    android:layout_marginLeft="@dimen/vs_5" />
            </LinearLayout>

            <!-- 点播输入区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="@dimen/vs_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:focusable="true"
                    android:gravity="center"
                    android:paddingRight="@dimen/vs_5"
                    android:text="点播"
                    android:textSize="@dimen/ts_22"
                    android:paddingEnd="@dimen/vs_5"
                    android:cursorVisible="false"
                    tools:ignore="RtlSymmetry,TextViewEdits" />

                <EditText
                    android:id="@+id/input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/input_dialog_api_input"
                    android:hint="请输入配置地址"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_2"
                    android:paddingTop="@dimen/vs_10"
                    android:paddingRight="@dimen/vs_2"
                    android:paddingBottom="@dimen/vs_10"
                    android:textColor="@color/color_CC000000"
                    android:textColorHint="@color/color_6C3D3D3D"
                    android:textSize="@dimen/ts_26" />

                <TextView
                    android:id="@+id/inputSubmit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="确定"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22"
                    android:layout_marginLeft="@dimen/vs_5" />
            </LinearLayout>

            <!-- 直播输入区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:focusable="true"
                    android:gravity="center"
                    android:paddingRight="@dimen/vs_5"
                    android:text="直播"
                    android:textSize="@dimen/ts_22"
                    android:paddingEnd="@dimen/vs_5"
                    android:cursorVisible="false"
                    tools:ignore="RtlSymmetry,TextViewEdits" />

                <EditText
                    android:id="@+id/inputLive"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/input_dialog_api_input"
                    android:hint="请输入直播地址"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingLeft="@dimen/vs_2"
                    android:paddingTop="@dimen/vs_10"
                    android:paddingRight="@dimen/vs_2"
                    android:paddingBottom="@dimen/vs_10"
                    android:textColor="@color/color_CC000000"
                    android:textColorHint="@color/color_6C3D3D3D"
                    android:textSize="@dimen/ts_26" />

                <TextView
                    android:id="@+id/inputSubmitLive"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/vs_5"
                    android:background="@drawable/button_dialog_main"
                    android:focusable="true"
                    android:gravity="center"
                    android:padding="@dimen/vs_10"
                    android:text="确定"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="@dimen/ts_22"
                    android:layout_marginLeft="@dimen/vs_5" />
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>
