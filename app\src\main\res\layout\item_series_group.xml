<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_marginRight="@dimen/vs_10"
    android:background="@drawable/shape_source_flag_focus"
    android:clickable="true"
    android:focusable="true"
    android:nextFocusUp="@id/mGridViewFlag"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_10"
    android:paddingTop="@dimen/vs_5"
    android:paddingRight="@dimen/vs_10"
    android:paddingBottom="@dimen/vs_1">

    <TextView
        android:id="@+id/tvSeriesGroup"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/vs_30"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:text=""
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_20" />
</LinearLayout>