# 本地数据按钮分页功能实现

## 概述
为TVBox应用的本地视频数据实现了按钮分页功能，支持每页30个项目的分页显示，使用分页按钮进行导航，替代了原有的滚动加载机制。

## 实现的功能

### 1. 分页数据加载
- **文件**: `LocalDataHelper.java`
- **方法**: `loadLocalVideosWithPagination(String rootPath, Context context, int page, int pageSize)`
- **功能**: 
  - 支持指定页码和每页大小的数据加载
  - 返回包含视频列表、当前页、总页数的结果对象
  - 保持原有的自然排序和过滤逻辑

### 2. 分页状态管理
- **文件**: `UserFragment.java`
- **变量**:
  - `localDataCurrentPage`: 当前页码
  - `localDataPageSize`: 每页大小（固定30）
  - `localDataTotalPages`: 总页数
  - `localDataLoading`: 是否正在加载

### 3. 分页按钮界面
- **文件**: `fragment_user.xml`
- **组件**:
  - 上一页按钮 (`btnPrevPage`)
  - 页码信息显示 (`tvPageInfo`)
  - 下一页按钮 (`btnNextPage`)
  - 分页控件容器 (`llPaginationControls`)

### 4. 电视端导航优化
- **焦点导航**: 支持遥控器上下左右导航
- **按钮状态**: 根据页码自动启用/禁用按钮
- **视觉反馈**: 按钮焦点变化和禁用状态的视觉效果

## 技术细节

### 分页计算逻辑
```java
int totalPages = (int) Math.ceil((double) totalVideos / pageSize);
int startIndex = (page - 1) * pageSize;
int endIndex = Math.min(startIndex + pageSize, totalVideos);
```

### 数据结构
```java
public static class LocalVideoPageResult {
    public List<Movie.Video> videos;
    public int currentPage;
    public int totalPages;
    public int totalCount;
}
```

### 按钮状态管理
```java
// 更新按钮状态
boolean hasPrevPage = localDataCurrentPage > 1;
boolean hasNextPage = localDataCurrentPage < localDataTotalPages;

btnPrevPage.setEnabled(hasPrevPage);
btnPrevPage.setAlpha(hasPrevPage ? 1.0f : 0.5f);
btnPrevPage.setFocusable(hasPrevPage);

btnNextPage.setEnabled(hasNextPage);
btnNextPage.setAlpha(hasNextPage ? 1.0f : 0.5f);
btnNextPage.setFocusable(hasNextPage);
```

## 使用方式

1. **启用本地数据模式**: 在设置中选择本地数据源
2. **按钮导航**: 使用上一页/下一页按钮进行页面切换
3. **页码显示**: 中间显示当前页/总页数信息
4. **电视遥控**: 支持遥控器方向键导航

## 界面布局

```
[历史] [搜索] [设置] [收藏] [本地数据]
              ↓
        [视频列表区域]
              ↓
    [上一页] [1/5] [下一页]
```

## 兼容性

- ✅ Android TV遥控器导航
- ✅ 手机触摸操作
- ✅ 保持原有的视频播放功能
- ✅ 保持原有的封面缓存机制
- ✅ 焦点管理和视觉反馈

## 电视端优化

- **焦点导航**: 顶部按钮可向下导航到分页按钮
- **边界处理**: 视频列表向下导航到分页按钮
- **按键处理**: 分页按钮向上导航回视频列表
- **状态管理**: 禁用的按钮不可获得焦点

## 测试

包含单元测试验证分页逻辑的正确性：
- 分页计算和边界条件
- 按钮状态管理
- 页码显示格式

## 性能优化

- 只加载当前页的数据，减少内存占用
- 保持原有的图片缓存机制
- 避免重复扫描文件系统
- 页面切换时滚动到顶部

## 主要变更

### 移除的功能
- LoadMore监听器和自动加载逻辑
- 滚动预加载机制
- 累积数据加载（addData）

### 新增的功能
- 分页按钮UI组件
- 按钮点击事件处理
- 页码状态管理
- 电视端焦点导航优化

## 文件变更列表

1. **LocalDataHelper.java** - 分页数据加载逻辑
2. **UserFragment.java** - 分页状态管理和按钮事件处理
3. **fragment_user.xml** - 分页按钮UI布局
4. **icon_left_arrow.xml** - 左箭头图标
5. **icon_right_arrow.xml** - 右箭头图标
6. **UserFragmentPaginationTest.java** - 分页功能单元测试
