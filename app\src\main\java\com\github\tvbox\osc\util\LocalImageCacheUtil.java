package com.github.tvbox.osc.util;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Base64;
import android.util.LruCache;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 本地图片Base64缓存工具类
 * 用于异步处理图片Base64转换并提供内存缓存
 */
public class LocalImageCacheUtil {
    
    private static final String TAG = "LocalImageCache";
    
    // 内存缓存，最大缓存50MB的Base64数据
    private static final LruCache<String, String> base64Cache = new LruCache<String, String>(50 * 1024 * 1024) {
        @Override
        protected int sizeOf(String key, String value) {
            return value.getBytes().length;
        }
    };
    
    // 正在处理的文件路径集合，避免重复处理
    private static final ConcurrentHashMap<String, Boolean> processingFiles = new ConcurrentHashMap<>();
    
    // 线程池用于异步处理
    private static final ExecutorService executor = Executors.newFixedThreadPool(2);
    
    // 主线程Handler
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());
    
    /**
     * 回调接口
     */
    public interface Base64Callback {
        void onResult(String base64Data);
        void onError(String error);
    }
    
    /**
     * 异步获取图片的Base64数据
     * @param imageFile 图片文件
     * @param callback 回调接口
     */
    public static void getBase64Async(File imageFile, Base64Callback callback) {
        if (imageFile == null || !imageFile.exists() || !imageFile.isFile()) {
            if (callback != null) {
                mainHandler.post(() -> callback.onError("文件不存在"));
            }
            return;
        }
        
        String filePath = imageFile.getAbsolutePath();
        
        // 检查缓存
        String cachedBase64 = base64Cache.get(filePath);
        if (!TextUtils.isEmpty(cachedBase64)) {
            if (callback != null) {
                mainHandler.post(() -> callback.onResult(cachedBase64));
            }
            return;
        }
        
        // 检查是否正在处理
        if (processingFiles.containsKey(filePath)) {
            // 正在处理中，延迟重试
            mainHandler.postDelayed(() -> getBase64Async(imageFile, callback), 100);
            return;
        }
        
        // 标记为正在处理
        processingFiles.put(filePath, true);
        
        // 异步处理
        executor.execute(() -> {
            try {
                String base64Data = convertImageToBase64Sync(imageFile);
                
                if (!TextUtils.isEmpty(base64Data)) {
                    // 缓存结果
                    base64Cache.put(filePath, base64Data);
                    
                    // 回调成功结果
                    if (callback != null) {
                        mainHandler.post(() -> callback.onResult(base64Data));
                    }
                } else {
                    // 回调错误
                    if (callback != null) {
                        mainHandler.post(() -> callback.onError("转换失败"));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                if (callback != null) {
                    mainHandler.post(() -> callback.onError("转换异常: " + e.getMessage()));
                }
            } finally {
                // 移除处理标记
                processingFiles.remove(filePath);
            }
        });
    }
    
    /**
     * 同步转换图片为Base64（在后台线程中调用）
     */
    private static String convertImageToBase64Sync(File imageFile) {
        if (imageFile == null || !imageFile.exists()) {
            return "";
        }
        
        FileInputStream fis = null;
        ByteArrayOutputStream baos = null;
        
        try {
            // 先检查文件大小，如果太大则压缩
            long fileSize = imageFile.length();
            Bitmap bitmap;
            
            if (fileSize > 500 * 1024) { // 大于500KB的图片进行压缩
                BitmapFactory.Options options = new BitmapFactory.Options();
                options.inSampleSize = calculateInSampleSize(imageFile, 300, 400);
                bitmap = BitmapFactory.decodeFile(imageFile.getAbsolutePath(), options);
                
                if (bitmap == null) {
                    return "";
                }
                
                baos = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 80, baos);
                byte[] imageBytes = baos.toByteArray();
                bitmap.recycle();
                
                return "data:image/jpeg;base64," + Base64.encodeToString(imageBytes, Base64.DEFAULT);
                
            } else {
                // 小文件直接读取
                fis = new FileInputStream(imageFile);
                baos = new ByteArrayOutputStream();
                
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
                
                byte[] imageBytes = baos.toByteArray();
                return "data:image/png;base64," + Base64.encodeToString(imageBytes, Base64.DEFAULT);
            }
            
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        } finally {
            try {
                if (fis != null) fis.close();
                if (baos != null) baos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 计算图片压缩比例
     */
    private static int calculateInSampleSize(File imageFile, int reqWidth, int reqHeight) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imageFile.getAbsolutePath(), options);
        
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        
        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        
        return inSampleSize;
    }
    
    /**
     * 获取缓存的Base64数据（同步方法）
     */
    public static String getCachedBase64(String filePath) {
        return base64Cache.get(filePath);
    }
    
    /**
     * 清除缓存
     */
    public static void clearCache() {
        base64Cache.evictAll();
        processingFiles.clear();
    }
    
    /**
     * 获取缓存大小信息
     */
    public static String getCacheInfo() {
        return "缓存条目: " + base64Cache.size() + ", 缓存大小: " + (base64Cache.size() / 1024) + "KB";
    }
}
