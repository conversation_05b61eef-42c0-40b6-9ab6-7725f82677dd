<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_40"
    android:paddingRight="@dimen/vs_40">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/tvUserHome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/tvHistory"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvLocalData"
                android:nextFocusRight="@+id/tvSearch"
                android:nextFocusDown="@+id/btnPrevPage"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_history" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="历史"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <!--
            <LinearLayout
                android:id="@+id/tvLive"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_live" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="直播"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>
            -->

            <LinearLayout
                android:id="@+id/tvSearch"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvHistory"
                android:nextFocusRight="@+id/tvFavorite"
                android:nextFocusDown="@+id/tvPageInfo"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_search" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="搜索"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <!--
            <LinearLayout
                android:id="@+id/tvPush"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_push" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="推送"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>
            -->

            <LinearLayout
                android:id="@+id/tvFavorite"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvSearch"
                android:nextFocusRight="@+id/tvLocalData"
                android:nextFocusDown="@+id/tvPageInfo"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_collect" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="收藏"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/tvSetting"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvFavorite"
                android:nextFocusRight="@+id/tvLocalData"
            android:nextFocusDown="@+id/btnPrevPage"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15"
                android:visibility="gone">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_setting" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="设置"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tvLocalData"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvFavorite"
                android:nextFocusRight="@+id/tvHistory"
                android:nextFocusDown="@+id/btnNextPage"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">

                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_folder" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="本地"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

        </LinearLayout>
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/tvHotList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="true"
            android:clipToPadding="false"
            android:layout_below="@+id/tvUserHome"
            android:layout_above="@+id/llPaginationControls"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_10"
            android:visibility="gone" />

        <!-- 分页控件 -->
        <LinearLayout
            android:id="@+id/llPaginationControls"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/vs_20"
            android:layout_marginBottom="@dimen/vs_20"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone">

            <!-- 上一页按钮 -->
            <LinearLayout
                android:id="@+id/btnPrevPage"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_80"
                android:layout_margin="@dimen/vs_10"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusUp="@+id/tvHistory"
                android:nextFocusRight="@+id/tvPageInfo"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingTop="@dimen/vs_10"
                android:paddingRight="@dimen/vs_20"
                android:paddingBottom="@dimen/vs_10">

                <ImageView
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_left_arrow"
                    android:scaleType="centerInside" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_5"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="上一页"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_30" />

            </LinearLayout>

            <!-- 页码信息 -->
            <TextView
                android:id="@+id/tvPageInfo"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_80"
                android:layout_margin="@dimen/vs_10"
                android:background="@drawable/shape_user_focus"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusUp="@+id/tvSearch"
                android:nextFocusLeft="@+id/btnPrevPage"
                android:nextFocusRight="@+id/btnNextPage"
                android:paddingLeft="@dimen/vs_20"
                android:paddingTop="@dimen/vs_10"
                android:paddingRight="@dimen/vs_20"
                android:paddingBottom="@dimen/vs_10"
                android:text="1/1"
                android:textAlignment="gravity"
                android:textColor="@color/color_CCFFFFFF"
                android:textSize="@dimen/ts_30" />

            <!-- 下一页按钮 -->
            <LinearLayout
                android:id="@+id/btnNextPage"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_80"
                android:layout_margin="@dimen/vs_10"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:gravity="center"
                android:nextFocusUp="@+id/tvLocalData"
                android:nextFocusLeft="@+id/tvPageInfo"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_20"
                android:paddingTop="@dimen/vs_10"
                android:paddingRight="@dimen/vs_20"
                android:paddingBottom="@dimen/vs_10">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginRight="@dimen/vs_5"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="下一页"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_30" />

                <ImageView
                    android:layout_width="@dimen/vs_30"
                    android:layout_height="@dimen/vs_30"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_right_arrow"
                    android:scaleType="centerInside" />

            </LinearLayout>

        </LinearLayout>

    </RelativeLayout>

</RelativeLayout>