<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_source_focus"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <TextView
        android:id="@+id/tvLiveAdd"
        android:layout_width="190mm"
        android:layout_height="50mm"
        android:ellipsize="end"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:paddingLeft="5mm"
        android:paddingRight="5mm"
        android:singleLine="true"
        android:text="+新增直播"
        android:textColor="@color/color_CBF46A"
        android:textSize="24mm"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvLive"
        android:layout_width="190mm"
        android:layout_height="50mm"
        android:ellipsize="end"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:gravity="center"
        android:paddingLeft="5mm"
        android:paddingRight="5mm"
        android:singleLine="true"
        android:textColor="@android:color/white"
        android:textSize="22mm" />
</FrameLayout>