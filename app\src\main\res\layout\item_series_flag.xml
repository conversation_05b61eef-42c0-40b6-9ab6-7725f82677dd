<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_marginRight="@dimen/vs_10"
    android:background="@drawable/shape_source_flag_focus"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_10"
    android:paddingTop="@dimen/vs_5"
    android:paddingRight="@dimen/vs_10"
    android:paddingBottom="@dimen/vs_1">

    <TextView
        android:id="@+id/tvSeriesFlag"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/vs_30"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:text="11111111111"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_20" />

    <View
        android:id="@+id/tvSeriesFlagSelect"
        android:layout_width="match_parent"
        android:layout_height="@dimen/vs_4"
        android:layout_marginLeft="@dimen/vs_5"
        android:layout_marginRight="@dimen/vs_5"
        android:background="@drawable/shape_source_flag_line" />
</LinearLayout>