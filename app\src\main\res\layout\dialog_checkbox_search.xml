<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/root"
        android:layout_width="@dimen/vs_420"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:background="@drawable/shape_dialog_pg_search_checkbox"
        android:orientation="vertical">


        <LinearLayout
            android:orientation="horizontal"
            android:weightSum="2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/checkAll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_gravity="center"
                android:gravity="center"
                android:background="@drawable/button_dialog_main"
                android:clickable="true"
                android:focusable="true"
                android:padding="@dimen/vs_30"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_26"
                android:text="全选" />

            <TextView
                android:id="@+id/clearAll"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/vs_10"
                android:layout_weight="1"
                android:layout_gravity="center"
                android:gravity="center"
                android:background="@drawable/button_dialog_main"
                android:clickable="true"
                android:focusable="true"
                android:padding="@dimen/vs_30"
                android:text="全不选"
                android:textColor="@color/color_FFFFFF"
                android:textSize="@dimen/ts_26" />
        </LinearLayout>

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"/>

    </LinearLayout>

</FrameLayout>