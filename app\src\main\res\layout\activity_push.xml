<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/ivQRCode"
            android:layout_width="@dimen/vs_300"
            android:layout_height="@dimen/vs_300"
            android:layout_gravity="center" />

        <TextView
            android:id="@+id/tvAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_20"
            android:gravity="center"
            android:lineSpacingMultiplier="1.5"
            android:textAlignment="gravity"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_24"
            tools:text="1111111111111111111111111111111111111111111111111111" />

        <TextView
            android:id="@+id/pushLocal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/vs_20"
            android:background="@drawable/shape_user_focus"
            android:focusable="true"
            android:gravity="center"
            android:padding="@dimen/vs_10"
            android:text="推送剪贴板内容"
            android:textColor="@color/color_FFFFFF"
            android:textSize="@dimen/ts_24" />
    </LinearLayout>
</FrameLayout>