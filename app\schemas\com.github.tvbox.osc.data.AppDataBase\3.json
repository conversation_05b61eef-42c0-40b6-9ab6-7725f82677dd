{"formatVersion": 1, "database": {"version": 3, "identityHash": "33b130e0cef69955d9d6fda2087674b0", "entities": [{"tableName": "cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `data` BLOB, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "BLOB", "notNull": false}], "primaryKey": {"columnNames": ["key"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "vodRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `data` BLOB, `dataJson` TEXT, `testMigration` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vodId", "columnName": "vodId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "data", "columnName": "data", "affinity": "BLOB", "notNull": false}, {"fieldPath": "dataJson", "columnName": "dataJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "testMigration", "columnName": "testMigration", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "localSource", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `api` TEXT NOT NULL, `type` INTEGER NOT NULL DEFAULT 0, `playerUrl` TEXT, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "api", "columnName": "api", "affinity": "TEXT", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "playerUrl", "columnName": "playerUrl", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["name"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "sourceState", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sourceKey` TEXT NOT NULL, `home` INTEGER NOT NULL, `active` INTEGER NOT NULL, `tidSort` TEXT, PRIMARY KEY(`sourceKey`))", "fields": [{"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": true}, {"fieldPath": "home", "columnName": "home", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "active", "columnName": "active", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tidSort", "columnName": "tidSort", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["sourceKey"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "localParse", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `url` TEXT NOT NULL, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["name"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "localLive", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`name` TEXT NOT NULL, `url` TEXT NOT NULL, PRIMARY KEY(`name`))", "fields": [{"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "url", "columnName": "url", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["name"], "autoGenerate": false}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '33b130e0cef69955d9d6fda2087674b0')"]}}