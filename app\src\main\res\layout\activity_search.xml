<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="@dimen/vs_420"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/vs_10"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvSearchCheckboxBtn"
                android:layout_width="match_parent"
                android:layout_height="28mm"
                android:background="@drawable/shape_user_focus"
                android:focusable="true"
                android:gravity="center"
                android:text="指定搜索源"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <EditText
                android:id="@+id/etSearch"
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_marginTop="@dimen/vs_10"
                android:background="@drawable/input_search"
                android:hint="请输入要搜索的内容"
                android:inputType="text"
                android:imeOptions="actionDone"
                android:maxLines="1"
                android:paddingLeft="@dimen/vs_10"
                android:paddingRight="@dimen/vs_10"
                android:textColor="@android:color/white"
                android:textColorHint="#99FFFFFF"
                android:textSize="@dimen/ts_20" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vs_50"
                android:layout_marginTop="@dimen/vs_10"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvSearch"
                    android:layout_width="0mm"
                    android:layout_height="@dimen/vs_50"
                    android:layout_weight="1"
                    android:background="@drawable/shape_user_focus"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="搜索"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20" />

                <Space
                    android:layout_width="@dimen/vs_10"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tvClear"
                    android:layout_width="0mm"
                    android:layout_height="@dimen/vs_50"
                    android:layout_weight="1"
                    android:background="@drawable/shape_user_focus"
                    android:focusable="true"
                    android:gravity="center"
                    android:text="清空"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_20" />
            </LinearLayout>
        </LinearLayout>

        <com.github.tvbox.osc.ui.tv.widget.SearchKeyboard
            android:id="@+id/keyBoardRoot"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <LinearLayout
            android:id="@+id/remoteRoot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/vs_20"
                android:gravity="center_horizontal"
                android:lineSpacingMultiplier="1.4"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_20" />

            <ImageView
                android:id="@+id/ivQRCode"
                android:layout_width="300mm"
                android:layout_height="300mm"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20mm"
                tools:ignore="InOrMmUsage" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llWord"
        android:layout_width="@dimen/vs_200"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="@dimen/vs_20"
        android:paddingBottom="@dimen/vs_20">

        <TextView
            android:id="@+id/wordSwitch"
            android:layout_width="@dimen/vs_200"
            android:background="@drawable/shape_user_focus"
            android:focusable="true"
            android:gravity="center"
            android:text="热词 | 历史"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_20"
            android:layout_marginBottom="@dimen/ts_10"
            android:layout_height="28mm"/>

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridViewWord"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/vs_20">

        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/mGridView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:visibility="invisible"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_5"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_5" />
    </LinearLayout>
</LinearLayout>