package com.github.tvbox.osc.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.InputDevice;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import static android.content.Context.UI_MODE_SERVICE;

import com.github.tvbox.osc.util.LOG;
import com.obsez.android.lib.filechooser.ChooserDialog;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.base.BaseActivity;
import com.github.tvbox.osc.util.DeviceUtils;
import com.github.tvbox.osc.util.FastClickCheckUtil;
import com.github.tvbox.osc.util.HawkConfig;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.orhanobut.hawk.Hawk;

import java.io.File;
import java.util.List;
import java.util.ArrayList;
import android.app.UiModeManager;
import android.content.pm.PackageManager;
import android.app.AlertDialog;
import android.content.DialogInterface;

public class LocalDataActivity extends BaseActivity {
    private TextView tvCurrentPath;
    private TextView tvDeviceType;
    private Button btnSelectPath;
    private Button btnConfirm;
    private String selectedPath = "";

    @Override
    protected int getLayoutResID() {
        return R.layout.activity_local_data;
    }

    @Override
    protected void init() {
        initView();
        initData();
    }

    private void initView() {
        tvCurrentPath = findViewById(R.id.tvCurrentPath);
        tvDeviceType = findViewById(R.id.tvDeviceType);
        btnSelectPath = findViewById(R.id.btnSelectPath);
        btnConfirm = findViewById(R.id.btnConfirm);

        // 检测设备类型并显示
        updateDeviceTypeDisplay();

        // 如果是智能电视，启用焦点管理
        if (DeviceUtils.shouldUseTVFocusManagement(this)) {
            setupTVFocusManagement();
        }

        btnSelectPath.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FastClickCheckUtil.check(v);
                checkPermissionAndSelectPath();
            }
        });

        btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                FastClickCheckUtil.check(v);
                if (selectedPath.isEmpty()) {
                    Toast.makeText(mContext, "请先选择数据目录", Toast.LENGTH_SHORT).show();
                    return;
                }
                
                // 保存路径
                Hawk.put(HawkConfig.LOCAL_DATA_PATH, selectedPath);
                
                // 设置首页推荐为本地数据
                Hawk.put(HawkConfig.HOME_REC, 3);
                
                // 返回主界面并刷新
                Intent intent = new Intent(mContext, HomeActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                Bundle bundle = new Bundle();
                bundle.putBoolean("useCache", false);
                intent.putExtras(bundle);
                startActivity(intent);
                finish();
            }
        });
    }

    private void initData() {
        // 显示当前保存的路径
        selectedPath = Hawk.get(HawkConfig.LOCAL_DATA_PATH, "");
        updatePathDisplay();
        
        // 检查存储权限状态（针对Android TV）
        if (isAndroidTV()) {
            checkStoragePermissionStatus();
        }
    }

    private void updatePathDisplay() {
        if (selectedPath.isEmpty()) {
            String displayText = "未选择目录";
            
            // 针对智能电视，在路径显示中加入权限状态提示
            if (isAndroidTV()) {
                boolean hasStoragePermission = XXPermissions.isGranted(this, Permission.Group.STORAGE);
                if (!hasStoragePermission) {
                    displayText += "\n\n⚠️ 需要存储权限才能访问USB设备";
                } else {
                    displayText += "\n\n✅ 存储权限已授权";
                }
            }
            
            tvCurrentPath.setText(displayText);
        } else {
            String displayText = selectedPath;
            
            // 针对智能电视，显示权限状态
            if (isAndroidTV()) {
                boolean hasStoragePermission = XXPermissions.isGranted(this, Permission.Group.STORAGE);
                displayText += hasStoragePermission ? "\n\n✅ 存储权限正常" : "\n\n⚠️ 存储权限可能已失效";
            }
            
            tvCurrentPath.setText(displayText);
        }
    }

    private void checkPermissionAndSelectPath() {
        // 检查是否为智能电视环境
        boolean isAndroidTV = isAndroidTV();
        
        if (XXPermissions.isGranted(this, Permission.Group.STORAGE)) {
            showFileChooser();
        } else {
            // 针对智能电视的特殊提示
            String permissionMessage = isAndroidTV ? 
                "智能电视需要存储权限才能访问USB存储设备和本地文件" : 
                "需要存储权限才能选择目录";
                
            XXPermissions.with(this)
                    .permission(Permission.Group.STORAGE)
                    .request(new OnPermissionCallback() {
                        @Override
                        public void onGranted(List<String> permissions, boolean all) {
                            if (all) {
                                if (isAndroidTV) {
                                    Toast.makeText(mContext, "存储权限已授权，可以访问USB存储设备", Toast.LENGTH_SHORT).show();
                                }
                                showFileChooser();
                            }
                        }

                        @Override
                        public void onDenied(List<String> permissions, boolean never) {
                            if (never) {
                                String message = isAndroidTV ? 
                                    "智能电视需要存储权限访问USB设备，请在系统设置中开启\n" +
                                    "注意：Android TV 14可能需要重启后重新授权" : 
                                    "获取存储权限失败，请在系统设置中开启";
                                    
                                Toast.makeText(mContext, message, Toast.LENGTH_LONG).show();
                                XXPermissions.startPermissionActivity(LocalDataActivity.this, permissions);
                            } else {
                                Toast.makeText(mContext, permissionMessage, Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
        }
    }

    private void showFileChooser() {
        // 如果是智能电视环境，先显示路径选择对话框
        if (isAndroidTV()) {
            showPathSelectionDialog();
        } else {
            // 手机/平板环境，直接使用文件选择器
            String initialPath = getInitialPath();
            openFileChooser(initialPath);
        }
    }
    
    /**
     * 显示路径选择对话框（针对智能电视）
     */
    private void showPathSelectionDialog() {
        List<String> availablePaths = getAvailableTVStoragePaths();
        
        if (availablePaths.isEmpty()) {
            Toast.makeText(mContext, "未找到可用的存储路径", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 如果只有一个路径，直接使用
        if (availablePaths.size() == 1) {
            openFileChooser(availablePaths.get(0));
            return;
        }
        
        // 准备显示列表
        String[] pathArray = new String[availablePaths.size()];
        String[] displayNames = new String[availablePaths.size()];
        
        for (int i = 0; i < availablePaths.size(); i++) {
            pathArray[i] = availablePaths.get(i);
            displayNames[i] = getPathDisplayName(availablePaths.get(i));
        }
        
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择存储路径");
        builder.setItems(displayNames, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openFileChooser(pathArray[which]);
            }
        });
        builder.setNegativeButton("取消", null);
        builder.show();
    }
    
    /**
     * 获取路径的显示名称
     */
    private String getPathDisplayName(String path) {
        if (path.contains("usb")) {
            return "USB存储 (" + path + ")";
        } else if (path.contains("sdcard") || path.contains("extsd")) {
            return "SD卡 (" + path + ")";
        } else if (path.contains("emulated")) {
            return "内部存储 (" + path + ")";
        } else {
            return "存储设备 (" + path + ")";
        }
    }
    
    /**
     * 打开文件选择器
     */
    private void openFileChooser(String initialPath) {
        new ChooserDialog(this)
                .withFilter(true, false) // 只显示文件夹
                .withStartFile(initialPath)
                .withChosenListener(new ChooserDialog.Result() {
                    @Override
                    public void onChoosePath(String path, File pathFile) {
                        if (pathFile != null && pathFile.isDirectory()) {
                            selectedPath = pathFile.getAbsolutePath();
                            updatePathDisplay();
                            Toast.makeText(mContext, "已选择目录: " + selectedPath, Toast.LENGTH_SHORT).show();
                        }
                    }
                })
                .build()
                .show();
    }
    
    /**
     * 获取初始路径，优先考虑智能电视的存储情况
     */
    private String getInitialPath() {
        // 如果已经有选择的路径，使用它
        if (!selectedPath.isEmpty()) {
            return selectedPath;
        }
        
        // 检查是否是智能电视环境
        if (DeviceUtils.isAndroidTV(this)) {
            return getBestTVStoragePath();
        } else {
            // 手机/平板环境，使用标准路径
            return "/storage/emulated/0/";
        }
    }
    
    /**
     * 检查是否为Android TV环境 - 使用多重检测策略
     */
    private boolean isAndroidTV() {
        // 使用DeviceUtils中的检测方法
        return DeviceUtils.isAndroidTV(this);
    }
    

    
    /**
     * 检查输入设备中是否有DPAD源
     */
    private boolean hasDpadInputDevice() {
        int[] deviceIds = InputDevice.getDeviceIds();
        for (int id : deviceIds) {
            InputDevice device = InputDevice.getDevice(id);
            if (device != null) {
                int sources = device.getSources();
                if ((sources & InputDevice.SOURCE_DPAD) == InputDevice.SOURCE_DPAD) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 更新设备类型显示
     */
    private void updateDeviceTypeDisplay() {
        boolean isTv = DeviceUtils.isAndroidTV(this);
        String deviceType = DeviceUtils.getDeviceTypeDescription(this);
        String focusStatus = isTv ? "（已启用焦点管理）" : "（触摸交互）";
        
        tvDeviceType.setText("设备类型：" + deviceType + " " + focusStatus);
        
        // 显示详细的检测信息
        String detailInfo = getDeviceDetectionDetails();
        if (!detailInfo.isEmpty()) {
            // tvDeviceType.setText(tvDeviceType.getText() + "\n" + detailInfo);
        }
    }
    
    /**
     * 获取设备检测的详细信息
     * 重用isTvOrBox中的检测逻辑
     */
    private String getDeviceDetectionDetails() {
        StringBuilder details = new StringBuilder();
        details.append("检测详情：");
        
        // 调用DeviceUtils的检测方法
        String detectionResult = getDeviceDetectionDetails(this);
        details.append(detectionResult);
        
        return details.toString();
    }
    
    /**
     * 获取设备检测详细信息
     */
    private String getDeviceDetectionDetails(Context context) {
        StringBuilder details = new StringBuilder();
        PackageManager pm = context.getPackageManager();
        UiModeManager uiMode = (UiModeManager) context.getSystemService(Context.UI_MODE_SERVICE);
        
        // 1. UI模式检测
        boolean isUiModeTV = uiMode != null && uiMode.getCurrentModeType() == Configuration.UI_MODE_TYPE_TELEVISION;
        if (isUiModeTV) {
            details.append(" TV-UI");
        } else {
            details.append(" no TV-UI");
        }
        
        // 2. 系统特性检测
        boolean hasLeanback = pm.hasSystemFeature(PackageManager.FEATURE_LEANBACK);
        boolean hasTelevision = pm.hasSystemFeature(PackageManager.FEATURE_TELEVISION);
        boolean hasFireTV = pm.hasSystemFeature("amazon.hardware.fire_tv");
        boolean hasGoogleTV = pm.hasSystemFeature("com.google.android.tv");
        boolean hasLeanbackOnly = pm.hasSystemFeature("android.software.leanback_only");
        
        if (hasLeanback) {
            details.append(" Leanback");
        } else {
            details.append(" 无Leanback");
        }
        
        if (hasTelevision) {
            details.append(" Television");
        } else {
            details.append(" 非Television");
        }
        
        if (hasFireTV) details.append(" FireTV");
        if (hasGoogleTV) details.append(" GoogleTV");
        if (hasLeanbackOnly) details.append(" LeanbackOnly");
        
        // 3. 触摸屏检测
        boolean hasTouchscreen = pm.hasSystemFeature(PackageManager.FEATURE_TOUCHSCREEN);
        if (!hasTouchscreen) {
            details.append(" 无触摸屏");
        } else {
            details.append(" 有触摸屏");
        }
        
        // 4. 电话功能检测
        boolean hasPhone = DeviceUtils.hasPhoneFeature(context);
        if (!hasPhone) {
            details.append(" 无电话");
        } else {
            details.append(" 有电话");
        }
        
        // 5. 屏幕特征检测
        boolean isLargeScreen = DeviceUtils.isLargeScreen(context);
        if (isLargeScreen) {
            details.append(" 大屏幕");
        } else {
            details.append(" 非大屏幕");
        }
        
        // 6. 物理遥控器/D-pad键检测
        boolean hasDpad = DeviceUtils.hasDpadKeys();
        if (hasDpad) {
            details.append(" D-pad键");
        } else {
            details.append(" 无D-pad键");
        }

        return details.toString();
    }

    /**
     * 设置智能电视的焦点管理
     */
    private void setupTVFocusManagement() {
        // 设置按钮的焦点属性
        btnSelectPath.setFocusable(true);
        btnSelectPath.setFocusableInTouchMode(false); // TV环境下不需要触摸模式焦点
        btnConfirm.setFocusable(true);
        btnConfirm.setFocusableInTouchMode(false);
        
        // 设置焦点导航
        btnSelectPath.setNextFocusRightId(R.id.btnConfirm);
        btnSelectPath.setNextFocusDownId(R.id.btnConfirm);
        btnConfirm.setNextFocusLeftId(R.id.btnSelectPath);
        btnConfirm.setNextFocusUpId(R.id.btnSelectPath);
        
        // 设置其他视图为不可获取焦点
        tvCurrentPath.setFocusable(false);
        tvCurrentPath.setFocusableInTouchMode(false);
        tvDeviceType.setFocusable(false);
        tvDeviceType.setFocusableInTouchMode(false);
        
        // 延迟设置默认焦点，确保布局完成
        btnSelectPath.post(new Runnable() {
            @Override
            public void run() {
                btnSelectPath.requestFocus();
            }
        });
    }
    
    /**
     * 获取Android TV环境下最佳的存储路径（保持兼容性）
     */
    private String getBestTVStoragePath() {
        List<String> availablePaths = getAvailableTVStoragePaths();
        return availablePaths.isEmpty() ? "/storage/emulated/0/" : availablePaths.get(0);
    }
    
    /**
     * 获取Android TV环境下所有可用的存储路径
     */
    private List<String> getAvailableTVStoragePaths() {
        List<String> availablePaths = new ArrayList<>();
        
        // 智能电视常见存储路径（按优先级排序）
        String[] tvStoragePaths = {
            "/storage/usb",           // USB存储（通用）
            "/storage/usbotg",        // USB OTG
            "/storage/usb1",          // USB存储1
            "/storage/usb2",          // USB存储2
            "/storage/usb3",          // USB存储3
            "/mnt/usb",               // 挂载的USB存储
            "/mnt/usbhost",           // USB主机存储
            "/mnt/usbhost1",          // USB主机存储1
            "/mnt/usbhost2",          // USB主机存储2
            "/mnt/media_rw/usb",      // 媒体读写USB
            "/mnt/media_rw/usbotg",   // 媒体读写USB OTG
            "/storage/external_storage", // 外部存储
            "/storage/sdcard1",       // SD卡1
            "/storage/extsd",         // 外部SD卡
            "/storage/emulated/0/",   // 内部存储（备选）
            "/sdcard/"                // SD卡（备选）
        };
        
        // 检查每个路径是否存在且可访问
        for (String path : tvStoragePaths) {
            File dir = new File(path);
            if (dir.exists() && dir.isDirectory() && dir.canRead()) {
                // 检查是否有写入权限
                if (dir.canWrite()) {
                    availablePaths.add(path);
                    continue;
                }
                // 如果只有读权限，检查是否有内容（可能是只读的媒体存储）
                File[] files = dir.listFiles();
                if (files != null && files.length > 0) {
                    availablePaths.add(path);
                }
            }
        }
        
        // 如果没有找到任何路径，至少添加默认路径
        if (availablePaths.isEmpty()) {
            availablePaths.add("/storage/emulated/0/");
        }
        
        return availablePaths;
    }
    
    /**
     * 检查存储权限状态并提供详细信息
     */
    private void checkStoragePermissionStatus() {
        boolean hasReadPermission = XXPermissions.isGranted(this, Permission.READ_EXTERNAL_STORAGE);
        boolean hasWritePermission = XXPermissions.isGranted(this, Permission.WRITE_EXTERNAL_STORAGE);
        
        if (isAndroidTV()) {
            String statusMessage = String.format(
                "存储权限状态：\n读取权限：%s\n写入权限：%s\n\n" +
                "提示：Android TV 14可能在重启后重置权限",
                hasReadPermission ? "已授权" : "未授权",
                hasWritePermission ? "已授权" : "未授权"
            );
            
            if (!hasReadPermission || !hasWritePermission) {
                statusMessage += "\n\n请点击\"选择目录\"按钮重新授权";
            }
            
            // 可以在需要时显示这个状态信息
            // Toast.makeText(mContext, statusMessage, Toast.LENGTH_LONG).show();
        }
    }
}