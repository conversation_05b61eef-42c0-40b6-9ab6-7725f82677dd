package com.github.tvbox.osc.util;

import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.app.UiModeManager;
import android.telephony.TelephonyManager;
import android.view.KeyCharacterMap;
import android.view.KeyEvent;

/**
 * 设备检测工具类
 * 用于检测设备类型（智能电视、手机、平板等）
 */
public class DeviceUtils {
    
    /**
     * 检查是否为Android TV环境 - 使用多重检测策略
     */
    public static boolean isAndroidTV(Context context) {
        PackageManager pm = context.getPackageManager();
        UiModeManager uiMode = (UiModeManager) context.getSystemService(Context.UI_MODE_SERVICE);
        
        // 1. UI模式检测
        if (uiMode != null && uiMode.getCurrentModeType() == Configuration.UI_MODE_TYPE_TELEVISION) {
            LOG.i("DeviceUtils UI模式检测 - Android TV");
            return true;
        }
        
        // 2. 系统特性检测
        if (pm.hasSystemFeature(PackageManager.FEATURE_LEANBACK)
                || pm.hasSystemFeature("android.software.leanback_only")   // 严格的Leanback模式
                || pm.hasSystemFeature(PackageManager.FEATURE_TELEVISION)
                || pm.hasSystemFeature("amazon.hardware.fire_tv")          // Amazon Fire TV
                || pm.hasSystemFeature("com.google.android.tv")) {         // Google TV
            LOG.i("DeviceUtils 系统特性检测 - Android TV");
            return true;
        }
        
        // 3. 触摸屏检测 - 大多数TV/机顶盒没有触摸屏
        if (!pm.hasSystemFeature(PackageManager.FEATURE_TOUCHSCREEN)) {
            LOG.i("DeviceUtils 触摸屏检测 - Android TV");
            return true;
        }
        
        // 4. 电话功能检测 - TV设备通常没有电话功能
        if (!hasPhoneFeature(context)) {
            // 进一步检查屏幕特征
            if (isLargeScreen(context)) {
                LOG.i("DeviceUtils 电话功能检测 - Android TV");
                return true;
            }
        }
        
        // 5. 物理遥控器/D-pad键检测
        if (hasDpadKeys()) {
            LOG.i("DeviceUtils 物理遥控器检测 - Android TV");
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查是否有电话功能
     */
    public static boolean hasPhoneFeature(Context context) {
        try {
            TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            return telephonyManager != null && telephonyManager.getPhoneType() != TelephonyManager.PHONE_TYPE_NONE;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 检查是否为大屏幕设备
     */
    public static boolean isLargeScreen(Context context) {
        int screenLayout = context.getResources().getConfiguration().screenLayout;
        int screenSize = screenLayout & Configuration.SCREENLAYOUT_SIZE_MASK;
        return screenSize >= Configuration.SCREENLAYOUT_SIZE_LARGE;
    }
    
    /**
     * 检查是否有物理D-pad键
     */
    public static boolean hasDpadKeys() {
        return KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_DPAD_UP)
                && KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_DPAD_DOWN)
                && KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_DPAD_LEFT)
                && KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_DPAD_RIGHT)
                && KeyCharacterMap.deviceHasKey(KeyEvent.KEYCODE_DPAD_CENTER);
    }
    
    /**
     * 获取设备类型描述
     */
    public static String getDeviceTypeDescription(Context context) {
        if (isAndroidTV(context)) {
            return "智能电视/机顶盒";
        } else if (isTablet(context)) {
            return "平板电脑";
        } else {
            return "手机";
        }
    }
    
    /**
     * 检查是否为平板设备
     */
    public static boolean isTablet(Context context) {
        return isLargeScreen(context) && hasPhoneFeature(context);
    }
    
    /**
     * 检查是否需要启用TV焦点管理
     */
    public static boolean shouldUseTVFocusManagement(Context context) {
        return isAndroidTV(context);
    }
} 