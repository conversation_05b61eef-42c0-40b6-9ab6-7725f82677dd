<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/color_6200EE</item>
        <item name="colorPrimaryDark">@color/color_3700B3</item>
        <item name="colorAccent">@color/color_03DAC5</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>//全屏即无通知栏
        <item name="android:windowContentOverlay">@null</item>//是否有遮盖
        <item name="windowNoTitle">true</item>
        <item name="android:keepScreenOn">true</item>
        <!--        <item name="android:windowBackground">@drawable/main_bg</item>-->
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <!-- 自定义dialog样式 -->
    <style name="CustomDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="CustomDialogStyleDim" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="epg_window_btn">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/item_bg_selector_right</item>
        <item name="android:paddingLeft">0dip</item>
        <item name="android:paddingRight">0dip</item>
        <item name="android:focusable">true</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:clickable">true</item>
        <item name="android:drawablePadding">10.0dip</item>
        <item name="android:radius">10dp</item>
    </style>
</resources>
