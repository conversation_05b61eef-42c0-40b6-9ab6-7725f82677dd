{"formatVersion": 1, "database": {"version": 1, "identityHash": "d1c2780b5424f0e960fe2364f63c86b8", "entities": [{"tableName": "cache", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`key` TEXT NOT NULL, `data` BLOB, PRIMARY KEY(`key`))", "fields": [{"fieldPath": "key", "columnName": "key", "affinity": "TEXT", "notNull": true}, {"fieldPath": "data", "columnName": "data", "affinity": "BLOB", "notNull": false}], "primaryKey": {"columnNames": ["key"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "vodRecord", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `dataJson` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vodId", "columnName": "vodId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dataJson", "columnName": "dataJson", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}, {"tableName": "vodCollect", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `vodId` TEXT, `updateTime` INTEGER NOT NULL, `sourceKey` TEXT, `name` TEXT, `pic` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "vodId", "columnName": "vodId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updateTime", "columnName": "updateTime", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sourceKey", "columnName": "sourceKey", "affinity": "TEXT", "notNull": false}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pic", "columnName": "pic", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd1c2780b5424f0e960fe2364f63c86b8')"]}}