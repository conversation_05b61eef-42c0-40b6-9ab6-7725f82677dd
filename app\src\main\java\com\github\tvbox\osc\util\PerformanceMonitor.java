package com.github.tvbox.osc.util;

import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控工具类
 * 用于监控本地影片加载的性能表现
 */
public class PerformanceMonitor {
    
    private static final String TAG = "PerformanceMonitor";
    
    // 计时器存储
    private static final Map<String, Long> timers = new HashMap<>();
    
    /**
     * 开始计时
     * @param tag 计时标签
     */
    public static void startTimer(String tag) {
        timers.put(tag, System.currentTimeMillis());
        Log.d(TAG, "[START] " + tag);
    }
    
    /**
     * 结束计时并输出结果
     * @param tag 计时标签
     * @return 耗时（毫秒）
     */
    public static long endTimer(String tag) {
        Long startTime = timers.get(tag);
        if (startTime == null) {
            Log.w(TAG, "[ERROR] Timer not found: " + tag);
            return -1;
        }
        
        long duration = System.currentTimeMillis() - startTime;
        Log.d(TAG, "[END] " + tag + " - Duration: " + duration + "ms");
        timers.remove(tag);
        return duration;
    }
    
    /**
     * 记录内存使用情况
     * @param tag 标签
     */
    public static void logMemoryUsage(String tag) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        Log.d(TAG, "[MEMORY] " + tag + " - Used: " + (usedMemory / 1024 / 1024) + "MB, " +
                "Total: " + (totalMemory / 1024 / 1024) + "MB, " +
                "Max: " + (maxMemory / 1024 / 1024) + "MB");
    }
    
    /**
     * 记录缓存状态
     */
    public static void logCacheStatus() {
        Log.d(TAG, "[CACHE] " + LocalDirectoryCacheUtil.getCacheInfo());
        Log.d(TAG, "[CACHE] " + LocalImageCacheUtil.getCacheInfo());
    }
    
    /**
     * 记录分页加载信息
     * @param page 当前页
     * @param pageSize 页大小
     * @param totalCount 总数
     * @param loadedCount 实际加载数
     */
    public static void logPaginationInfo(int page, int pageSize, int totalCount, int loadedCount) {
        Log.d(TAG, "[PAGINATION] Page: " + page + "/" + ((totalCount + pageSize - 1) / pageSize) +
                ", PageSize: " + pageSize + ", Total: " + totalCount + ", Loaded: " + loadedCount);
    }
}
