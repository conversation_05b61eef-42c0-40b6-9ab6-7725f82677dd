package com.github.tvbox.osc.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.util.Base64;

import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.bean.VodInfo;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalDataHelper {
    
    private static final String[] VIDEO_EXTENSIONS = {
        ".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".3gp", ".m4v", ".webm", ".ts", ".m3u8"
    };
    
    private static final String COVER_FILE_NAME = "cover.png";
    
    // 影片信息数据类
    public static class MovieInfo {
        public String cover_url;
        public String title;
        public String description;
    }
    
    // 缓存影片信息数据
    private static List<MovieInfo> movieInfoList = null;
    
    /**
     * 从assets加载影片信息数据
     */
    private static List<MovieInfo> loadMovieInfoData(Context context) {
        if (movieInfoList != null) {
            return movieInfoList;
        }
        
        try {
            InputStream inputStream = context.getAssets().open("data.json");
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            inputStream.close();
            
            String jsonString = baos.toString("UTF-8");
            baos.close();
            
            Gson gson = new Gson();
            Type listType = new TypeToken<List<MovieInfo>>(){}.getType();
            movieInfoList = gson.fromJson(jsonString, listType);
            
            return movieInfoList != null ? movieInfoList : new ArrayList<MovieInfo>();
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }
    
    /**
     * 根据影片名称匹配影片信息
     */
    private static MovieInfo findMovieInfo(Context context, String videoName) {
        if (TextUtils.isEmpty(videoName)) {
            return null;
        }
        
        List<MovieInfo> infoList = loadMovieInfoData(context);
        if (infoList.isEmpty()) {
            return null;
        }
        
        // 处理影片名称：如果第一个字符是大写英文字符，去掉
        String processedName = videoName;
        if (processedName.length() > 0 && Character.isUpperCase(processedName.charAt(0)) && 
            processedName.charAt(0) >= 'A' && processedName.charAt(0) <= 'Z') {
            processedName = processedName.substring(1);
        }
        
        // 匹配逻辑：data.json的title是空格分割的，影片名称匹配其中的子部分即为符合
        for (MovieInfo info : infoList) {
            if (TextUtils.isEmpty(info.title)) {
                continue;
            }
            
            String[] titleParts = info.title.split("\\s+");
            for (String part : titleParts) {
                if (!TextUtils.isEmpty(part) && processedName.contains(part)) {
                    return info;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 自然排序工具方法，支持文件名中的数字正确排序
     * 例如：第1集.mp4 < 第2集.mp4 < 第10集.mp4 < 第20集.mp4
     * 而不是字典序：第1集.mp4 < 第10集.mp4 < 第2集.mp4 < 第20集.mp4
     */
    private static int compareNaturally(String str1, String str2) {
        if (str1 == null && str2 == null) return 0;
        if (str1 == null) return -1;
        if (str2 == null) return 1;
        
        Pattern NUMBER_PATTERN = Pattern.compile("(\\d+)");
        Matcher m1 = NUMBER_PATTERN.matcher(str1);
        Matcher m2 = NUMBER_PATTERN.matcher(str2);
        
        int pos1 = 0, pos2 = 0;
        
        while (m1.find() && m2.find()) {
            // 比较数字前的文本部分
            String prefix1 = str1.substring(pos1, m1.start());
            String prefix2 = str2.substring(pos2, m2.start());
            int prefixCompare = prefix1.compareToIgnoreCase(prefix2);
            if (prefixCompare != 0) {
                return prefixCompare;
            }
            
            // 比较数字部分
            int num1 = Integer.parseInt(m1.group());
            int num2 = Integer.parseInt(m2.group());
            if (num1 != num2) {
                return Integer.compare(num1, num2);
            }
            
            pos1 = m1.end();
            pos2 = m2.end();
        }
        
        // 比较剩余的文本部分
        String suffix1 = str1.substring(pos1);
        String suffix2 = str2.substring(pos2);
        return suffix1.compareToIgnoreCase(suffix2);
    }
    
    /**
     * File对象的自然排序比较器
     */
    private static final Comparator<File> FILE_NATURAL_COMPARATOR = new Comparator<File>() {
        @Override
        public int compare(File f1, File f2) {
            return compareNaturally(f1.getName(), f2.getName());
        }
    };
    
    /**
     * VodSeries对象的自然排序比较器
     */
    private static final Comparator<VodInfo.VodSeries> NATURAL_COMPARATOR = new Comparator<VodInfo.VodSeries>() {
        @Override
        public int compare(VodInfo.VodSeries o1, VodInfo.VodSeries o2) {
            return compareNaturally(o1.name, o2.name);
        }
    };
    
    /**
     * Movie.Video对象的自然排序比较器
     */
    private static final Comparator<Movie.Video> VIDEO_NATURAL_COMPARATOR = new Comparator<Movie.Video>() {
        @Override
        public int compare(Movie.Video v1, Movie.Video v2) {
            return compareNaturally(v1.name, v2.name);
        }
    };
    
    /**
     * 加载本地视频数据
     */
    public static List<Movie.Video> loadLocalVideos(String rootPath) {
        List<Movie.Video> videos = new ArrayList<>();
        
        if (TextUtils.isEmpty(rootPath)) {
            return videos;
        }
        
        File rootDir = new File(rootPath);
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            return videos;
        }
        
        File[] subDirs = rootDir.listFiles();
        if (subDirs == null) {
            return videos;
        }
        
        for (File subDir : subDirs) {
            if (subDir.isDirectory()) {
                Movie.Video video = createVideoFromDirectory(subDir, null);
                if (video != null) {
                    videos.add(video);
                }
            }
        }
        
        // 对本地视频列表进行自然排序
        Collections.sort(videos, VIDEO_NATURAL_COMPARATOR);
        
        return videos;
    }
    
    /**
     * 加载本地视频数据（带Context参数）
     */
    public static List<Movie.Video> loadLocalVideos(String rootPath, Context context) {
        List<Movie.Video> videos = new ArrayList<>();

        if (TextUtils.isEmpty(rootPath)) {
            return videos;
        }

        File rootDir = new File(rootPath);
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            return videos;
        }

        File[] subDirs = rootDir.listFiles();
        if (subDirs == null) {
            return videos;
        }

        for (File subDir : subDirs) {
            if (subDir.isDirectory()) {
                Movie.Video video = createVideoFromDirectory(subDir, context);
                if (video != null) {
                    videos.add(video);
                }
            }
        }

        // 对本地视频列表进行自然排序
        Collections.sort(videos, VIDEO_NATURAL_COMPARATOR);

        return videos;
    }

    /**
     * 分页加载本地视频数据
     * @param rootPath 根目录路径
     * @param context 上下文
     * @param page 页码（从1开始）
     * @param pageSize 每页数量
     * @return 分页结果对象
     */
    public static LocalVideoPageResult loadLocalVideosWithPagination(String rootPath, Context context, int page, int pageSize) {
        LocalVideoPageResult result = new LocalVideoPageResult();

        // 获取所有本地视频
        List<Movie.Video> allVideos = loadLocalVideos(rootPath, context);

        // 计算分页信息
        int totalCount = allVideos.size();
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);

        // 计算当前页的起始和结束索引
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalCount);

        // 获取当前页的数据
        List<Movie.Video> pageVideos = new ArrayList<>();
        if (startIndex < totalCount) {
            pageVideos = allVideos.subList(startIndex, endIndex);
        }

        // 设置结果
        result.videos = pageVideos;
        result.currentPage = page;
        result.totalPages = totalPages;
        result.totalCount = totalCount;
        result.pageSize = pageSize;
        result.hasMore = page < totalPages;

        return result;
    }

    /**
     * 本地视频分页结果类
     */
    public static class LocalVideoPageResult {
        public List<Movie.Video> videos = new ArrayList<>();
        public int currentPage = 1;
        public int totalPages = 1;
        public int totalCount = 0;
        public int pageSize = 30;
        public boolean hasMore = false;
    }
    
    /**
     * 从目录创建视频对象
     */
    private static Movie.Video createVideoFromDirectory(File dir, Context context) {
        File[] files = dir.listFiles();
        if (files == null) {
            return null;
        }
        
        // 查找视频文件
        List<File> videoFiles = new ArrayList<>();
        File coverFile = null;
        
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName().toLowerCase();
                
                // 检查是否是封面文件
                if (fileName.equals(COVER_FILE_NAME)) {
                    coverFile = file;
                    continue;
                }
                
                // 检查是否是视频文件
                for (String ext : VIDEO_EXTENSIONS) {
                    if (fileName.endsWith(ext)) {
                        videoFiles.add(file);
                        break;
                    }
                }
            }
        }
        
        // 如果没有找到视频文件，跳过这个目录
        if (videoFiles.isEmpty()) {
            return null;
        }
        
        Movie.Video video = new Movie.Video();
        video.id = dir.getAbsolutePath();
        video.name = dir.getName();
        video.sourceKey = "local";
        
        // 设置封面图片
        if (coverFile != null && coverFile.exists()) {
            video.pic = convertImageToBase64(coverFile);
        } else if (context != null) {
            // 如果cover.png不存在，根据影片名称匹配data.json获取cover_url
            MovieInfo movieInfo = findMovieInfo(context, dir.getName());
            if (movieInfo != null && !TextUtils.isEmpty(movieInfo.cover_url)) {
                // 保存原始URL，让Adapter层统一处理缓存
                video.pic = movieInfo.cover_url;
                // 移除异步缓存，避免重复请求
                // if (!LocalCoverCacheUtil.isCached(movieInfo.cover_url)) {
                //     LocalCoverCacheUtil.cacheNetworkCover(movieInfo.cover_url, null);
                // }
            } else {
                video.pic = "";
            }
        } else {
            video.pic = "";
        }
        
        return video;
    }
    
    /**
     * 将图片文件转换为Base64字符串
     */
    private static String convertImageToBase64(File imageFile) {
        try {
            FileInputStream fis = new FileInputStream(imageFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            byte[] imageBytes = baos.toByteArray();
            fis.close();
            baos.close();
            
            return "data:image/png;base64," + Base64.encodeToString(imageBytes, Base64.DEFAULT);
        } catch (IOException e) {
            e.printStackTrace();
            return "";
        }
    }
    
    /**
     * 获取目录中的视频文件列表
     */
    public static List<File> getVideoFiles(String dirPath) {
        List<File> videoFiles = new ArrayList<>();
        
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            return videoFiles;
        }
        
        File[] files = dir.listFiles();
        if (files == null) {
            return videoFiles;
        }
        
        for (File file : files) {
            if (file.isFile()) {
                String fileName = file.getName().toLowerCase();
                for (String ext : VIDEO_EXTENSIONS) {
                    if (fileName.endsWith(ext)) {
                        videoFiles.add(file);
                        break;
                    }
                }
            }
        }
        
        // 对视频文件进行自然排序
        Collections.sort(videoFiles, FILE_NATURAL_COMPARATOR);
        
        return videoFiles;
    }
    
    /**
     * 创建本地视频的VodInfo对象
     */
    public static VodInfo createLocalVodInfo(String dirPath, String title) {
        return createLocalVodInfo(dirPath, title, null);
    }
    
    /**
     * 创建本地视频的VodInfo对象（带Context参数）
     */
    public static VodInfo createLocalVodInfo(String dirPath, String title, Context context) {
        VodInfo vodInfo = new VodInfo();
        vodInfo.id = dirPath;
        vodInfo.name = title;
        vodInfo.sourceKey = "local";
        
        // 根据影片名称匹配data.json获取简介
        if (context != null) {
            MovieInfo movieInfo = findMovieInfo(context, title);
            if (movieInfo != null && !TextUtils.isEmpty(movieInfo.description)) {
                vodInfo.des = movieInfo.description;
            } else {
                vodInfo.des = "本地视频";
            }
        } else {
            vodInfo.des = "本地视频";
        }
        
        // 设置封面
        File coverFile = new File(dirPath, COVER_FILE_NAME);
        if (coverFile.exists()) {
            vodInfo.pic = convertImageToBase64(coverFile);
        } else if (context != null) {
            // 如果cover.png不存在，根据影片名称匹配data.json获取cover_url
            MovieInfo movieInfo = findMovieInfo(context, title);
            if (movieInfo != null && !TextUtils.isEmpty(movieInfo.cover_url)) {
                // 保存原始URL，让Adapter层统一处理缓存
                vodInfo.pic = movieInfo.cover_url;
                // 移除异步缓存，避免重复请求
                // if (!LocalCoverCacheUtil.isCached(movieInfo.cover_url)) {
                //     LocalCoverCacheUtil.cacheNetworkCover(movieInfo.cover_url, null);
                // }
            } else {
                vodInfo.pic = "";
            }
        } else {
            vodInfo.pic = "";
        }
        
        // 初始化必要的集合
        vodInfo.seriesMap = new LinkedHashMap<>();
        vodInfo.seriesFlags = new ArrayList<>();
        
        // 按层级扫描视频文件
        scanVideosByLevel(new File(dirPath), vodInfo.seriesMap);
        
        if (vodInfo.seriesMap.isEmpty()) {
            return null;
        }
        
        // 设置默认播放标志和索引
        if (vodInfo.seriesMap.containsKey("默认")) {
            vodInfo.playFlag = "默认";
        } else {
            vodInfo.playFlag = vodInfo.seriesMap.keySet().iterator().next();
        }
        vodInfo.playIndex = 0;
        
        // 设置seriesFlags，用于UI显示分组选项
        for (String flagName : vodInfo.seriesMap.keySet()) {
            VodInfo.VodSeriesFlag flag = new VodInfo.VodSeriesFlag(flagName);
            if (flagName.equals(vodInfo.playFlag)) {
                flag.selected = true;
            }
            vodInfo.seriesFlags.add(flag);
        }
        
        return vodInfo;
    }
    
    /**
     * 按层级扫描视频文件
     */
    private static void scanVideosByLevel(File rootDir, LinkedHashMap<String, List<VodInfo.VodSeries>> seriesMap) {
        if (!rootDir.exists() || !rootDir.isDirectory()) {
            return;
        }
        
        File[] files = rootDir.listFiles();
        if (files == null) {
            return;
        }
        
        // 2级：直接在影片目录下的视频文件，添加到"默认"分组
        List<VodInfo.VodSeries> defaultSeries = new ArrayList<>();
        List<File> subDirs = new ArrayList<>();
        
        for (File file : files) {
            if (file.isFile() && isVideoFile(file.getName())) {
                VodInfo.VodSeries series = new VodInfo.VodSeries();
                series.name = file.getName();
                series.url = "file://" + file.getAbsolutePath();
                defaultSeries.add(series);
            } else if (file.isDirectory()) {
                subDirs.add(file);
            }
        }
        
        // 如果2级目录下存在视频文件，添加到"默认"分组
        if (!defaultSeries.isEmpty()) {
            Collections.sort(defaultSeries, NATURAL_COMPARATOR);
            seriesMap.put("默认", defaultSeries);
        }
        
        // 对子目录进行自然排序，确保分组名按正确顺序显示
        Collections.sort(subDirs, FILE_NATURAL_COMPARATOR);
        
        // 3级：季度目录处理
        for (File subDir : subDirs) {
            String seasonName = subDir.getName();
            List<VodInfo.VodSeries> seasonSeries = new ArrayList<>();
            
            File[] seasonFiles = subDir.listFiles();
            if (seasonFiles != null) {
                for (File seasonFile : seasonFiles) {
                    if (seasonFile.isFile() && isVideoFile(seasonFile.getName())) {
                        // 3级目录下的视频文件
                        VodInfo.VodSeries series = new VodInfo.VodSeries();
                        series.name = seasonFile.getName();
                        series.url = "file://" + seasonFile.getAbsolutePath();
                        seasonSeries.add(series);
                    }
                    // 4级目录直接忽略处理，不进行递归
                }
            }
            
            // 对3级目录中的视频文件进行自然排序
            if (!seasonSeries.isEmpty()) {
                Collections.sort(seasonSeries, NATURAL_COMPARATOR);
            }
            
            // 即使3级目录没视频文件，也要显示分组名
            seriesMap.put(seasonName, seasonSeries);
        }
        
        // 重新排序，确保"默认"分组排在最前面
        if (seriesMap.containsKey("默认")) {
            LinkedHashMap<String, List<VodInfo.VodSeries>> sortedMap = new LinkedHashMap<>();
            List<VodInfo.VodSeries> defaultGroup = seriesMap.remove("默认");
            sortedMap.put("默认", defaultGroup);
            sortedMap.putAll(seriesMap);
            seriesMap.clear();
            seriesMap.putAll(sortedMap);
        }
    }
    
    /**
     * 检查文件是否是视频文件
     */
    public static boolean isVideoFile(String fileName) {
        if (TextUtils.isEmpty(fileName)) {
            return false;
        }
        
        String lowerName = fileName.toLowerCase();
        for (String ext : VIDEO_EXTENSIONS) {
            if (lowerName.endsWith(ext)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 搜索本地影片
     */
    public static List<Movie.Video> searchLocalVideos(String searchTitle) {
        return searchLocalVideos(searchTitle, null);
    }
    
    /**
     * 搜索本地影片（带Context参数）
     */
    public static List<Movie.Video> searchLocalVideos(String searchTitle, Context context) {
        List<Movie.Video> searchResults = new ArrayList<>();
        
        // 获取本地数据路径
        String localPath = com.orhanobut.hawk.Hawk.get(com.github.tvbox.osc.util.HawkConfig.LOCAL_DATA_PATH, "");
        if (TextUtils.isEmpty(localPath)) {
            return searchResults;
        }
        
        // 加载所有本地视频
        List<Movie.Video> allLocalVideos = context != null ? 
            loadLocalVideos(localPath, context) : loadLocalVideos(localPath);
        
        // 执行搜索匹配
        if (!TextUtils.isEmpty(searchTitle)) {
            String searchTitleLower = searchTitle.toLowerCase().trim();
            String[] searchWords = searchTitleLower.split("\\s+");
            
            for (Movie.Video video : allLocalVideos) {
                if (matchVideoName(video.name, searchWords)) {
                    searchResults.add(video);
                }
            }
        }
        
        // 对搜索结果进行自然排序
        Collections.sort(searchResults, VIDEO_NATURAL_COMPARATOR);
        
        return searchResults;
    }
    
    /**
     * 匹配视频名称
     */
    private static boolean matchVideoName(String videoName, String[] searchWords) {
        if (TextUtils.isEmpty(videoName) || searchWords == null || searchWords.length == 0) {
            return false;
        }
        
        String videoNameLower = videoName.toLowerCase();
        int matchCount = 0;
        
        for (String word : searchWords) {
            if (videoNameLower.contains(word)) {
                matchCount++;
            }
        }
        
        // 要求所有搜索词都匹配
        return matchCount == searchWords.length;
    }
    

} 