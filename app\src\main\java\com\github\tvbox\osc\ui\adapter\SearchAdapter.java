package com.github.tvbox.osc.ui.adapter;

import android.text.TextUtils;
import android.widget.ImageView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.github.tvbox.osc.R;
import com.github.tvbox.osc.api.ApiConfig;
import com.github.tvbox.osc.bean.Movie;
import com.github.tvbox.osc.bean.SourceBean;
import com.github.tvbox.osc.picasso.RoundTransformation;
import com.github.tvbox.osc.util.HawkConfig;
import com.github.tvbox.osc.util.ImgUtil;
import com.github.tvbox.osc.util.LocalCoverCacheUtil;
import com.github.tvbox.osc.util.MD5;
import com.orhanobut.hawk.Hawk;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.io.File;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class SearchAdapter extends BaseQuickAdapter<Movie.Video, BaseViewHolder> {
    public SearchAdapter() {
        super(Hawk.get(HawkConfig.SEARCH_VIEW, 1) == 0 ? R.layout.item_search_lite : R.layout.item_search, new ArrayList<>());
    }

    @Override
    protected void convert(BaseViewHolder helper, Movie.Video item) {
        // 获取源名称，特殊处理本地源
        String sourceName;
        if ("local".equals(item.sourceKey)) {
            sourceName = "本地影片";
        } else {
            SourceBean sourceBean = ApiConfig.get().getSource(item.sourceKey);
            sourceName = sourceBean != null ? sourceBean.getName() : "未知源";
        }
        
        // lite
        if (Hawk.get(HawkConfig.SEARCH_VIEW, 1) == 0) {
            helper.setText(R.id.tvName, String.format("%s  %s %s %s", sourceName, item.name, item.type == null ? "" : item.type, item.note == null ? "" : item.note));
        } else {// with preview
            helper.setText(R.id.tvName, item.name);
            helper.setText(R.id.tvSite, sourceName);
            helper.setVisible(R.id.tvNote, item.note != null && !item.note.isEmpty());
            if (item.note != null && !item.note.isEmpty()) {
                helper.setText(R.id.tvNote, item.note);
            }
            ImageView ivThumb = helper.getView(R.id.ivThumb);
            if (!TextUtils.isEmpty(item.pic)) {
                item.pic = item.pic.trim();
                if (item.pic.startsWith("data:image")) {
                    // Base64图片
                    ivThumb.setImageBitmap(ImgUtil.decodeBase64ToBitmap(item.pic));
                } else if (item.pic.startsWith("http") && "local".equals(item.sourceKey)) {
                    // 本地影片的网络封面，使用缓存工具类
                    String cachedPic = LocalCoverCacheUtil.getCachedCover(item.pic);
                    if (!cachedPic.equals(item.pic) && new File(cachedPic).exists()) {
                        // 已缓存到本地文件，使用文件路径加载
                        Picasso.get()
                                .load(new File(cachedPic))
                                .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                        .centerCorp(true)
                                        .override(AutoSizeUtils.mm2px(mContext, ImgUtil.defaultWidth), AutoSizeUtils.mm2px(mContext, ImgUtil.defaultHeight))
                                        .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                .placeholder(R.drawable.img_loading_placeholder)
                                .noFade()
                                .error(ImgUtil.createTextDrawable(item.name))
                                .into(ivThumb);
                    } else {
                        // 还未缓存，先显示占位图，然后异步缓存并更新
                        // ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
                        final String finalUrl = item.pic;
                        final String finalName = item.name;
                        final ImageView finalIvThumb = ivThumb;
                        LocalCoverCacheUtil.cacheNetworkCover(item.pic, new LocalCoverCacheUtil.CacheCallback() {
                            @Override
                            public void onResult(String result) {
                                if (!result.equals(finalUrl) && new File(result).exists()) {
                                    // 缓存完成，更新显示
                                    if (finalIvThumb.getContext() instanceof android.app.Activity) {
                                        ((android.app.Activity) finalIvThumb.getContext()).runOnUiThread(() -> {
                                            Picasso.get()
                                                    .load(new File(result))
                                                    .transform(new RoundTransformation(MD5.string2MD5(finalUrl))
                                                            .centerCorp(true)
                                                            .override(AutoSizeUtils.mm2px(mContext, ImgUtil.defaultWidth), AutoSizeUtils.mm2px(mContext, ImgUtil.defaultHeight))
                                                            .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                                                    .noFade()
                                                    .error(ImgUtil.createTextDrawable(finalName))
                                                    .into(finalIvThumb);
                                        });
                                    }
                                }
                            }
                        });
                    }
                } else {
                    // 普通网络图片或其他类型
                    Picasso.get()
                            .load(item.pic)
                            .transform(new RoundTransformation(MD5.string2MD5(item.pic))
                                    .centerCorp(true)
                                    .override(AutoSizeUtils.mm2px(mContext, ImgUtil.defaultWidth), AutoSizeUtils.mm2px(mContext, ImgUtil.defaultHeight))
                                    .roundRadius(AutoSizeUtils.mm2px(mContext, 10), RoundTransformation.RoundType.ALL))
                            .placeholder(R.drawable.img_loading_placeholder)
                            .noFade()
                            .error(ImgUtil.createTextDrawable(item.name))
                            .into(ivThumb);
                }
            } else {
                ivThumb.setImageDrawable(ImgUtil.createTextDrawable(item.name));
            }
        }
    }
}